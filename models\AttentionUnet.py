import torch
from torch import nn
import torch.nn.functional as F
from unet_parts import *


class Attention_UNet(nn.Module):
	#inspired by UNet taken from: https://github.com/milesial/Pytorch-UNet/blob/master/unet/unet_model.py
	
	def __init__(self, hidden_size=64, bilinear=True):
		super(Attention_UNet, self).__init__()
		self.hidden_size = hidden_size
		self.bilinear = bilinear

		self.inc = DoubleConv(9, hidden_size)
		self.down1 = Down(hidden_size, 2*hidden_size)
		self.down2 = Down(2*hidden_size, 4*hidden_size)
		self.down3 = Down(4*hidden_size, 8*hidden_size)
		factor = 2 if bilinear else 1
		self.down4 = Down(8*hidden_size, 16*hidden_size // factor)
		self.up1 = Up(16*hidden_size, 8*hidden_size // factor, bilinear)
		self.up2 = Up(8*hidden_size, 4*hidden_size // factor, bilinear)
		self.up3 = Up(4*hidden_size, 2*hidden_size // factor, bilinear)
		self.up4 = Up(2*hidden_size, hidden_size, bilinear) 
		# self.outc = OutConv(hidden_size, hidden_size)
		self.out_ln = nn.LayerNorm(hidden_size)
		self.out_scalar = nn.Linear(16*hidden_size // factor,3)
        
	def forward(self,graph_node):
     
		x1 = self.inc(graph_node.pixel)
		x2 = self.down1(x1)
		x3 = self.down2(x2)
		x4 = self.down3(x3)
		x5 = self.down4(x4)
		x = self.up1(x5, x4)
		x = self.up2(x, x3)
		x = self.up3(x, x2)
		x = self.up4(x, x1)

		# grid sample to point cloud
		rt_x = []
		for i in range(graph_node.num_graphs):
			mask = i == graph_node.batch
			cur_query = graph_node.pixel_query[mask]
			cur_query = cur_query.reshape(1,-1,1,2)
			cur_x = (
				torch.nn.functional.grid_sample(
					x[i : i + 1], cur_query, align_corners=False
				)
				.squeeze()
				.T
			)

			rt_x.append(cur_x)

		rt_x = torch.cat(rt_x, dim=0)
		x_scalar = self.out_scalar(torch.amax(x5,dim=[2,3]))

		return self.out_ln(rt_x),x_scalar

