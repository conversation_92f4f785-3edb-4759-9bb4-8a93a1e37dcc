import sys
import os

cur_path = os.path.split(__file__)[0]
sys.path.append(cur_path)

import torch
from torch.optim import Adam
import numpy as np
torch.autograd.set_detect_anomaly(True)

# from dataset import Load_mesh
from utils import get_param
import time
from utils.get_param import get_hyperparam
from utils.Logger import Logger
from utils.utilities import NodeType
from models.model import EulerSolver
from torch_geometric.nn import global_add_pool
from torch_geometric.data.batch import Batch
from dataset import Graph_loader
# from dataset import Load_mesh
from math import ceil
import random
import datetime


# configurate parameters
params = get_param.params()


torch.set_float32_matmul_precision('high')


# configurate parameters
params = get_param.params()
seed = int(datetime.datetime.now().timestamp())
np.random.seed(seed)
random.seed(seed)
torch.manual_seed(seed)
torch.cuda.set_per_process_memory_fraction(0.99, params.on_gpu)
torch.set_num_threads(os.cpu_count() // 2)
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# initialize Logger and load model / optimizer if according parameters were given
logger = Logger(
    get_hyperparam(params),
    use_dat=True,
    params=params,
    copy_code=True,
)



# initialize Training Dataset
start = time.time()
datasets_factory = Graph_loader.DatasetFactory(
    params=params,
    dataset_dir=params.dataset_dir,
    state_save_dir=logger.saving_path,
    device=device,
)

# refresh dataset size
params.dataset_size = datasets_factory.dataset_size

# create dataset objetc
datasets, loader, sampler = datasets_factory.create_datasets(
    batch_size=params.batch_size, num_workers=0, pin_memory=False
)

end = time.time()
print("Training traj has been loaded time consuming:{0}".format(end - start))

# initialize fluid model
# model = HybridOptimizer(
#     message_passing_num=params.message_passing_num,
#     node_input_size=params.node_input_size,
#     edge_input_size=params.edge_input_size,
#     node_output_size=params.node_output_size,
#     hidden_size=params.hidden_size,
#     device=device,
# )


# opt_model = model.to(device)
# opt_model.train()
# optimizer = Adam(opt_model.parameters(), lr=params.lr)


# lr_scheduler = torch.optim.lr_scheduler.MultiStepLR(optimizer, milestones=params.milestones, gamma=params.gamma)
 

# if (
#     params.load_latest
#     or params.load_date_time is not None
#     or params.load_index is not None
# ):
#     logger.load_logger(datetime=params.load_date_time)
#     # load_logger = Logger(get_hyperparam(params),use_csv=False,use_tensorboard=params.log,params=params,git_info=git_info)
#     if params.load_optimizer:
#         params.load_date_time, params.load_index = logger.load_state(
#             model=opt_model,
#             optimizer=optimizer,
#             scheduler=lr_scheduler,
#             datetime=params.load_date_time,
#             index=params.load_index,
#             device=device,
#         )
#     else:
#         params.load_date_time, params.load_index = logger.load_state(
#             model=opt_model,
#             optimizer=None,
#             scheduler=None,
#             datetime=params.load_date_time,
#             index=params.load_index,
#             device=device,
#         )
#     params.load_index = int(params.load_index)
#     optimizer.param_groups[0]["lr"] = params.lr
#     print(f"loaded: {params.load_date_time}, {params.load_index}")
# params.load_index = 0 if params.load_index is None else params.load_index



n_epochs = params.n_epochs


graph_edge, graph_cell, graph_map, graph_Index = [
        data.to(device) for data in next(iter(loader))
    ]
        

Solver = EulerSolver(graph_edge,graph_cell,graph_map,graph_Index)

current_primes = graph_cell.x.clone()

for epoch in range(n_epochs):

    
    start = time.time()
    updated_primes = Solver.solv_lusgs(current_primes)

    # 检查是否出现NaN
    if torch.isnan(updated_primes).any():
        print(f"WARNING: NaN detected in updated_primes at epoch {epoch}!")
        print(f"NaN locations: {torch.isnan(updated_primes).sum().item()} out of {updated_primes.numel()} elements")
        # 可选：打印具体的NaN位置
        nan_mask = torch.isnan(updated_primes)
        if nan_mask.any():
            print(f"First few NaN positions: {torch.where(nan_mask)[0][:10].tolist()}")
        # 可选：停止训练或使用之前的值
        # break  # 如果需要停止训练，取消注释这行
        # updated_primes = torch.where(nan_mask, current_primes, updated_primes)  # 用之前的值替换NaN

    current_primes = updated_primes

    # 计算残差值（每个epoch都计算）
    rhs_max, rhs_mean, rhs_l2_norm = 0.0, 0.0, 0.0
    if hasattr(Solver, 'last_rhs') and Solver.last_rhs is not None:
        rhs_abs = torch.abs(Solver.last_rhs)
        rhs_max = torch.max(rhs_abs).item()
        rhs_mean = torch.mean(rhs_abs).item()
        rhs_l2_norm = torch.norm(rhs_abs).item()
 
    # 输出此epoch的处理时间
    print(f"Epoch {epoch} completed in {time.time() - start:.2f} seconds")

    # Print the headers and values
    if epoch%1 == 0:
        # Using formatted string for better readability
        headers = ["Epoch", "Epoch Time", "Max RHS", "Mean RHS", "L2 Norm RHS"]
        values = [
            epoch,
            f"{time.time() - start:.2f}s",
            f"{rhs_max:.3e}",
            f"{rhs_mean:.3e}",
            f"{rhs_l2_norm:.3e}"
        ]

        # Determine the maximum width for each column
        column_widths = [max(len(str(x)), len(headers[i])) + 2 for i, x in enumerate(values)]

        # Create a format string for each row
        row_format = "".join(["{:<" + str(width) + "}" for width in column_widths])

        print(row_format.format(*headers))
        print(row_format.format(*values))

    if epoch%1000 == 0:
        global_idx = graph_cell.global_idx.cpu()
        batch = graph_cell.batch.cpu()
        datasets.payback_for_vis(current_primes.cpu(),global_idx,params.dataset_size,batch)

 
       


    
 
