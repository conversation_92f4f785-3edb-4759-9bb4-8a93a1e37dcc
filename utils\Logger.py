import os
import sys
import time

import datetime as dt

from natsort import natsorted
import json
import shutil
cur_path = os.path.split(__file__)[0]
sys.path.append(cur_path)

# import utilities
class Logger:
    def __init__(
        self,
        name,
        datetime=None,
        use_dat=False,
        params=None,
        saving_path=None,
        copy_code=True,
    ):
        """
        Logger logs metrics to CSV files / tensorboard
        :name: logging name (e.g. model name / dataset name / ...)
        :datetime: date and time of logging start (useful in case of multiple runs). Default: current date and time is picked
        :use_dat: log output to csv files (needed for plotting)
        :use_tensorboard: log output to tensorboard
        """
        self.name = name
        self.params = params
        if datetime:
            self.datetime = datetime
        else:
            self.datetime = dt.datetime.now().strftime("%Y-%m-%d-%H:%M:%S")
        if saving_path is not None:
            self.saving_path = saving_path
        else:
            self.saving_path = os.getcwd() + f"/Logger/{name}/{self.datetime}"

        source_valid_file_path = os.path.split(os.path.split(__file__)[0])[0]
        target_valid_file_path = f"{self.saving_path}/source"

        if copy_code:
            os.makedirs(f"{self.saving_path}/source", exist_ok=True)
            shutil.copytree(
                source_valid_file_path,
                target_valid_file_path,
                ignore=self.ignore_files_and_folders,
                dirs_exist_ok=True,
            )

        self.target_valid_file_path = target_valid_file_path + "/validate.py"
  

        self.use_dat = use_dat
        if use_dat:
            os.makedirs("Logger/{}/{}/logs".format(name, self.datetime), exist_ok=True)
       
        self.best_loss = 0

    

    def ignore_files_and_folders(self, dir_name, names):
        ignored = set()
        files_to_ignore = {}
        folders_to_ignore = {"rollout", "Logger","grid_example"}

        for name in names:
            path = os.path.join(dir_name, name)

            if os.path.isfile(path) and name in files_to_ignore:
                ignored.add(name)
            elif os.path.isdir(path) and name in folders_to_ignore:
                ignored.add(name)

        return ignored

    def log(self, item, value, index):
        """
        log index value couple for specific item into csv file / tensorboard
        :item: string describing item (e.g. "training_loss","test_loss")
        :value: value to log
        :index: index (e.g. batchindex / epoch)
        """

        if self.use_dat:
            filename = "Logger/{}/{}/logs/{}.dat".format(self.name, self.datetime, item)

            if os.path.exists(filename):
                append_write = "a"
            else:
                append_write = "w"

            with open(filename, append_write) as log_file:
                log_file.write("{}, {}\n".format(index, value))

     



    def save_state(self, model, optimizer, scheduler, index="final",loss=None):
        """
        saves state of model and optimizer
        :model: model to save (if list: save multiple models)
        :optimizer: optimizer (if list: save multiple optimizers)
        :index: index of state to save (e.g. specific epoch)
        """
       
        os.makedirs(self.saving_path + "/states", exist_ok=True)
        path = self.saving_path + "/states"

        with open(path + "/commandline_args.json", "wt") as f:
            json.dump(
                {**vars(self.params)}, f, indent=4, ensure_ascii=False
            )

        model.save_checkpoint(path + "/{}.state".format(index), optimizer, scheduler)
        # self.best_loss = loss
        return path + "/{}.state".format(index)


    def load_state(
        self,
        model,
        optimizer,
        scheduler,
        datetime=None,
        index=None,
        continue_datetime=False,
        device=None,
    ):
        """
        loads state of model and optimizer
        :model: model to load (if list: load multiple models)
        :optimizer: optimizer to load (if list: load multiple optimizers; if None: don't load)
        :datetime: date and time from run to load (if None: take latest folder)
        :index: index of state to load (e.g. specific epoch) (if None: take latest index)
        :continue_datetime: flag whether to continue on this run. Default: False
        :return: datetime, index (helpful, if datetime / index wasn't given)
        """

        if datetime is None:
            for _, dirs, _ in os.walk("Logger/{}/".format(self.name)):
                datetime = sorted(dirs)[-1]
                if datetime == self.datetime:
                    datetime = sorted(dirs)[-2]
                break

        if continue_datetime:
            # CODO: remove generated directories...
            os.rmdir()
            self.datetime = datetime

        if index is None:
            for _, _, files in os.walk(
                "Logger/{}/{}/states/".format(self.name, datetime)
            ):
                index = os.path.splitext(natsorted(files)[-1])[0]
                break

        path = "Logger/{}/{}/states/{}.state".format(self.name, datetime, index)

        model.load_checkpoint(
            optimizer=optimizer, scheduler=scheduler, ckpdir=path, device=device
        )

        return datetime, index


    def load_logger(self, datetime=None, load=False):
        """
        copy older tensorboard logger to new dir
        :datetime: date and time from run to load (if None: take latest folder)
        """

        if datetime is None:
            for _, dirs, _ in os.walk("Logger/{}/".format(self.name)):
                datetime = sorted(dirs)[-1]
                if datetime == self.datetime:
                    datetime = sorted(dirs)[-2]
                break

        if load:
            cwd = os.getcwd()
            path = "Logger/{0}/{1}/tensorboard/".format(self.name, datetime)
            for _, _, files in os.walk(path):
                for file in files:
                    older_tensorboard_n = file
                    older_tensorboard = path + older_tensorboard_n

                    newer_tensorboard = (
                        cwd
                        + "/Logger/{0}/{1}/tensorboard/".format(
                            self.name, self.datetime
                        )
                        + older_tensorboard_n
                    )
                    shutil.copyfile(older_tensorboard, newer_tensorboard)
                break

            if os.path.exists(newer_tensorboard):
                print(
                    "older tensorboard aleady been copied to {0}".format(
                        newer_tensorboard
                    )
                )


t_start = 0


def t_step():
    """
    returns delta t from last call of t_step()
    """
    global t_start
    t_end = time.perf_counter()
    delta_t = t_end - t_start
    t_start = t_end
    return delta_t
