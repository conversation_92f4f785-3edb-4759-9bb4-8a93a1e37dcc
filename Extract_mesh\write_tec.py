import os
import sys

sys.path.insert(0, os.path.split(os.path.abspath(__file__))[0])
import numpy as np

def write_array_to_file(field, file_handle):
    """
    将NumPy数组每5个元素为一组写入文件，每组占一行。在文件末尾添加一个换行符。
    
    参数:
    - field: NumPy数组，维度为[10000]，类型为np.float32。
    - filename: 要写入数据的文件名。
    """
    # 确保数组是一维的
    assert field.ndim == 1, "数组必须是一维的"

    # 每5个元素为一组，格式化为字符串并写入一行
    for i in range(0, len(field), 5):
        # 提取当前组的元素，并将其转换为字符串列表
        line = ' '.join(map(str, field[i:i+5]))
        # 写入一行数据
        file_handle.write(line + '\n')
    
    # 在文件最后写入一个换行符
    file_handle.write('\n')
        



def formatnp(data, file_handle, amounts_per_line=3):
    """
    Write formatted numpy array data to a file, with each line containing a specified number of elements.

    Arguments:
        - data: a list or numpy array of data to write.
        - file_handle: an open file handle for writing.
        - amounts_per_line: the number of data elements per line (default is 3).
    """
    for i in range(len(data)):
        if np.issubdtype(data[i], np.integer):
            file_handle.write(" {:d}".format(data[i].item()))
        else:
            file_handle.write(" {:e}".format(data[i].item()))
        if (i + 1) % amounts_per_line == 0:
            file_handle.write("\n")
    
    # Ensure the file ends with a newline character
    if len(data) % amounts_per_line != 0:
        file_handle.write("\n")


def has_more_than_three_duplicates(arr):
    unique, counts = np.unique(arr, return_counts=True)
    return np.any(counts > 3)


def count_cells_num_node(arr):
    unique, counts = np.unique(arr, return_counts=True)
    return counts


def write_cell_index(Cells, Cells_index, writer):
    # print("start running has_more_than_three_duplicates")
    rtval = has_more_than_three_duplicates(Cells_index)

    FE_num_nodes_counter = 0
    cell_data_to_write = []  # Use a list to accumulate the data to be written for each line
    # print("start writing individual cell index to file")
    for index in range(Cells.shape[0]):
        cell_value_str = str(int(Cells[index]))

        # Always add the current cell's value to the list
        cell_data_to_write.append(cell_value_str)
        FE_num_nodes_counter += 1

        # Check conditions to decide whether to write the current cell's data
        is_last_cell = index == Cells.shape[0] - 1
        if not is_last_cell:
            next_cell_differs = not is_last_cell and Cells_index[index] != Cells_index[index + 1]
        else:
            next_cell_differs = False

        # If this is the last cell or the next cell's index is different, write the data
        if is_last_cell or next_cell_differs:
            # If there are more than three duplicates and not enough nodes, add the current cell again
            if rtval and FE_num_nodes_counter <= 3:
                cell_data_to_write.append(cell_value_str)

            # Write the accumulated cell data as a single space-separated string, then reset
            if cell_data_to_write:  # 如果cell_data_to_write非空
                writer.write(" " + " ".join(cell_data_to_write) + "\n")
            else:  # 如果cell_data_to_write为空
                writer.write("\n")
            cell_data_to_write = []  # Reset for the next group of cells
            FE_num_nodes_counter = 0
            
def write_face_index(faces, writer):
    for index in range(faces.shape[0]):
        formatnp(faces[index], writer, amounts_per_line=2)






def write_tecplotzone(
    filename="flowcfdgcn.dat",
    datasets=None,
    time_step_length=100,

):
    interior_zone = datasets[0]
    mu = interior_zone["mu"]
    rho = interior_zone["rho"]
    dt  = interior_zone["dtau"]

    with open(filename, "w") as f:
        f.write('TITLE = "MetaFDGN solution"\n')
        f.write('VARIABLES = "X"\n"Y"\n"U"\n"V"\n"P"\n"RHO"\n')
        f.write('DATASETAUXDATA Common.Incompressible="False"\n')
        f.write('DATASETAUXDATA Common.VectorVarsAreVelocity="TRUE"\n')
        f.write(f'DATASETAUXDATA Common.Viscosity="{mu}"\n')
        f.write(f'DATASETAUXDATA Common.Density="{rho}"\n')
        f.write(f'DATASETAUXDATA Common.UVar="3"\n')
        f.write(f'DATASETAUXDATA Common.VVar="4"\n')
        f.write(f'DATASETAUXDATA Common.PressureVar="5"\n')
        f.write(f'DATASETAUXDATA Common.DensityVar="6"\n')

        for i in range(time_step_length):
            for zone in datasets:
                zonename = zone["zonename"]
                if zonename == "Fluid":
                    f.write('ZONE T="{0}"\n'.format(zonename))

                    X = zone["mesh_pos"][:, 0]
                    Y = zone["mesh_pos"][:, 1]
                    U = zone["velocity"][:, 0]
                    V = zone["velocity"][:, 1]
                    P = zone["pressure"][:, 0]
                    RHO = zone["density"][:, 0]
                    field = np.concatenate((X, Y, U, V, P, RHO), axis=0)
                    Cells = zone["cells"] + 1
                    Cells_index = zone["cells_index"]
                    # face_node = zone["face_node"]
          
                    f.write(" STRANDID=1, SOLUTIONTIME={0}\n".format(dt * i))
                    counts = count_cells_num_node(Cells_index)
                    write_face = False
                    if counts.max() <= 3:
                        f.write(
                            f" Nodes={X.size}, Elements={Cells_index.max().item()+1}, "
                            "ZONETYPE=FETRIANGLE\n"
                        )
                        write_face = False
                    elif 3 < counts.max() <= 4:
                        f.write(
                            f" Nodes={X.size}, Elements={Cells_index.max().item()+1}, "
                            "ZONETYPE=FEQuadrilateral\n"
                        )
                        write_face = False
                    elif counts.max() > 4:
                        f.write(
                            f" Nodes={X.size}, Faces={face_node.shape[1]},Elements={Cells_index.max().item()+1}, "
                            "ZONETYPE=FEPolygon\n"
                        )
                        f.write(
                            f"NumConnectedBoundaryFaces=0, TotalNumBoundaryConnections=0\n"
                        )
                        write_face = True

                    f.write(" DATAPACKING=BLOCK\n")
                    data_packing_type = zone["data_packing_type"]
                    if data_packing_type == "cell":
                        f.write(" VARLOCATION=([3,4,5,6]=CELLCENTERED)\n")
                    elif data_packing_type == "node":
                        f.write(" VARLOCATION=([3,4,5,6]=NODAL)\n")
                    f.write(" DT=(SINGLE SINGLE SINGLE SINGLE SINGLE SINGLE)\n")
                    try:
                        print(f"start writing interior field data, size in {field.size},shape in {field.shape}")
                        write_array_to_file(field, f)
                    except Exception as e:
                        print(f"Error formatting data: {e}")
                        
                    print("Start writing interior cell")
                    if not write_face:
                        write_cell_index(Cells, Cells_index, f)
     
                elif (
                    zonename == "OBSTACLE_BOUNDARY" or zonename.find("BOUNDARY") != -1
                ):
                    f.write('ZONE T="{0}"\n'.format(zonename))
                    X = zone["mesh_pos"][i, :, 0].astype(np.float32)
                    Y = zone["mesh_pos"][i, :, 1].astype(np.float32)
                    U = zone["velocity"][i, :, 0]
                    V = zone["velocity"][i, :, 1]
                    P = zone["pressure"][i, :, 0]
                    RHO = zone["density"][i, :, 0]
                    field = np.concatenate((X, Y, U, V, P, RHO), axis=0)
                    faces = zone["face_node"]+ 1
                    f.write(" STRANDID=3, SOLUTIONTIME={0}\n".format(dt * i))
                    f.write(
                        f" Nodes={X.size}, Elements={faces.shape[0]}, "
                        "ZONETYPE=FELineSeg\n"
                    )
                    f.write(" DATAPACKING=BLOCK\n")
                    f.write('AUXDATA Common.BoundaryCondition="Wall"\n')
                    f.write('AUXDATA Common.IsBoundaryZone="TRUE"\n')
                    data_packing_type = zone["data_packing_type"]
                    if data_packing_type == "cell":
                        f.write(" VARLOCATION=([3,4,5]=CELLCENTERED)\n")
                    elif data_packing_type == "node":
                        f.write(" VARLOCATION=([3,4,5]=NODAL)\n")
                    f.write(" DT=(SINGLE SINGLE SINGLE SINGLE SINGLE )\n")
                    
                    print("start writing boundary field data")
                    write_array_to_file(field, f)
                    
                    print("start writing boundary face")
                    write_face_index(faces, f)
                    
    print("saved tecplot file at " + filename)
