from torch import tensor, zeros, ones, sum, max, sqrt, float
from torch.nn import <PERSON><PERSON><PERSON>
import torch
from torch.nn.functional import normalize
from torch.autograd import Function
from torch_scatter import scatter, scatter_add


class Normalizer(Module):
    def __init__(self, size, max_accumulations=10**7, epsilon=1e-8, device=None):
        """
        Online normalization module

        size: feature dimension
        max_accumulation: maximum number of batches
        epsilon: std cutoff for constant variable
        device: pytorch device
        """

        super(Normalizer, self).__init__()

        self.max_accumulations = max_accumulations
        self.epsilon = epsilon

        # self.register_buffer('acc_count', tensor(0, dtype=float, device=device))
        # self.register_buffer('num_accumulations', tensor(0, dtype=float, device=device))
        # self.register_buffer('acc_sum', zeros(size, dtype=float, device=device))
        # self.register_buffer('acc_sum_squared', zeros(size, dtype=float, device=device))

        self.register_buffer("acc_count", tensor(1.0, dtype=float, device=device))
        self.register_buffer(
            "num_accumulations", tensor(1.0, dtype=float, device=device)
        )
        self.register_buffer("acc_sum", ones(size, dtype=float, device=device))
        self.register_buffer("acc_sum_squared", ones(size, dtype=float, device=device))

    def forward(self, batched_data, accumulate=True):
        """
        Updates mean/standard deviation and normalizes input data

        batched_data: batch of data
        accumulate: if True, update accumulation statistics
        """
        if accumulate and self.num_accumulations < self.max_accumulations:
            self._accumulate(batched_data)

        return (batched_data - self._mean().to(batched_data.device)) / self._std().to(
            batched_data.device
        )

    def inverse(self, normalized_batch_data):
        """
        Unnormalizes input data
        """

        return normalized_batch_data * self._std().to(
            normalized_batch_data.device
        ) + self._mean().to(normalized_batch_data.device)

    def _accumulate(self, batched_data):
        """
        Accumulates statistics for mean/standard deviation computation
        """
        count = tensor(batched_data.shape[0]).float()
        data_sum = sum(batched_data, dim=0)
        squared_data_sum = sum(batched_data**2, dim=0)

        self.acc_sum += data_sum.to(self.acc_sum.device)
        self.acc_sum_squared += squared_data_sum.to(self.acc_sum_squared.device)
        self.acc_count += count.to(self.acc_count.device)
        self.num_accumulations += 1

    def _mean(self):
        """
        Returns accumulated mean
        """
        safe_count = max(self.acc_count, tensor(1.0).float())

        return self.acc_sum / safe_count

    def _std(self):
        """
        Returns accumulated standard deviation
        """
        safe_count = max(self.acc_count, tensor(1.0).float())
        std = sqrt(self.acc_sum_squared / safe_count - self._mean() ** 2)

        std[std < self.epsilon] = 1.0

        return std






eps = 1e-12

class NormalizeGrads(Function):
    """
    一个为图数据设计的自定义反向传播函数。
    
    在前向传播中，它是一个恒等函数。
    在反向传播中，它接收传回的梯度，并对一个批次中每个图的梯度进行独立的L2归一化。
    """
    @staticmethod
    def forward(ctx, input_tensor, batch_vector):
        """
        在前向传播中，我们只保存 batch_vector 以便在反向传播中使用。
        :param ctx: 上下文对象
        :param input_tensor: 任意需要进行梯度归一化的张量，形状为 [Total_Nodes, Features]
        :param batch_vector: 批次向量，形状为 [Total_Nodes]，指示每个节点属于哪个图
        :return: 未经修改的 input_tensor
        """
        # 检查 batch_vector 是否为长整型
        if not batch_vector.dtype == torch.long:
            raise TypeError("batch_vector 必须是 torch.long 类型")
        ctx.save_for_backward(batch_vector)
        return input_tensor

    @staticmethod
    def backward(ctx, grad_output):
        """
        这是真正的魔法发生的地方。
        :param ctx: 上下文对象
        :param grad_output: 流回的梯度，形状为 [Total_Nodes, Features] 或 [Total_Nodes]
        :return: (修改后的梯度, None)，因为 batch_vector 不需要梯度
        """
        # grad_output 的形状可能是 [Total_Nodes, Features] 或 [Total_Nodes]
        batch, = ctx.saved_tensors

        # 1. 鲁棒地计算每个节点梯度的 L2 范数的平方
        #    这是修复问题的关键点。
        if grad_output.dim() > 1:
            # 情况一：梯度是二维的 [Total_Nodes, Features]
            grad_norms_sq = grad_output.pow(2).sum(dim=-1)
        else:
            # 情况二：梯度是一维的 [Total_Nodes]
            # 此时，每个元素本身就是特征，直接平方即可
            grad_norms_sq = grad_output.pow(2)
        
        # 确保 grad_norms_sq 是一维的
        if grad_norms_sq.dim() == 0:
            grad_norms_sq = grad_norms_sq.unsqueeze(0)

        # 2. 使用 scatter_add 将每个图的节点梯度范数平方相加
        # 结果的形状是 [Num_Graphs]
        graph_grad_norms_sq = scatter_add(grad_norms_sq, batch, dim=0)

        # 3. 计算每个图的 L2 范数，并增加一个小的 epsilon 以防止除以零
        graph_grad_norms = (graph_grad_norms_sq.sqrt() + eps).clamp(min=eps)

        # 4. "广播" 或 "映射" 每个图的范数回到其对应的每个节点上
        # graph_grad_norms[batch] 的形状将是 [Total_Nodes]
        # unsqueeze(-1) 是为了让形状变为 [Total_Nodes, 1] 以便广播
        node_specific_graph_norms = graph_grad_norms[batch].unsqueeze(-1)
        
        # 5. 对梯度进行归一化
        normalized_grad = grad_output / node_specific_graph_norms
        
        # # 6. (可选但推荐) 裁剪梯度以增加稳定性
        # normalized_grad = normalized_grad.clamp(min=-10, max=10)

        # 7. 返回修改后的梯度。batch_vector 的梯度是 None
        return normalized_grad, None

# 创建一个方便调用的接口
normalize_grads = NormalizeGrads.apply

# ============================================================================
# 图数据归一化功能 (新增)
# ============================================================================

def graph_norm(grads, batch, eps=1e-40):
    """
    在图数据上执行与张量数据等价的范数计算操作。
    
    与ref项目不同，这里对每个图的每个通道分别计算归一化因子，
    充分利用图数据的灵活性，避免CNN结构的限制。
    
    Args:
        grads: 图节点的梯度张量，形状为 [Total_Nodes, Features]
        batch: 批次向量，形状为 [Total_Nodes]，指示每个节点属于哪个图
        eps: 防止除零的小值
    
    Returns:
        graph_grad_std: 每个节点每个通道对应的归一化因子，形状为 [Total_Nodes, Features]
    """
    # 1. 计算每个节点每个通道梯度的平方
    node_grad_sq = grads.pow(2)  # [Total_Nodes, Features]
    
    # 2. 使用 scatter_add 将每个图的每个通道的梯度平方相加
    # 这里需要对每个通道分别进行 scatter 操作
    num_graphs = batch.max().item() + 1
    num_features = grads.shape[1]
    
    # 为每个图每个通道计算梯度平方和
    graph_grad_sq_sum = torch.zeros(num_graphs, num_features, device=grads.device, dtype=grads.dtype)
    for i in range(num_features):
        graph_grad_sq_sum[:, i] = scatter_add(node_grad_sq[:, i], batch, dim=0)
    
    # 3. 计算每个图的节点数量
    ones = torch.ones_like(batch, dtype=torch.float64)
    graph_node_counts = scatter_add(ones, batch, dim=0)  # [Num_Graphs]
    
    # 4. 计算每个图每个通道的归一化因子
    # 相当于 sqrt(sum(grad²)) / sqrt(node_count) 对每个通道分别计算
    graph_grad_std = (graph_grad_sq_sum.sqrt() / graph_node_counts.unsqueeze(-1).sqrt()).clamp_min(eps)
    
    # 5. 将每个图每个通道的标准差映射回对应的节点
    node_grad_std = graph_grad_std[batch]  # [Total_Nodes, Features]
    
    return node_grad_std.detach()








