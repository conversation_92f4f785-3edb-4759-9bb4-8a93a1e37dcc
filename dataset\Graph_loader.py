"""Utility functions for reading the datasets."""

import sys
import os
file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)

from torch_scatter import scatter_mean
from torch_geometric.data import InMemoryDataset
from torch_geometric.data import Data

import matplotlib
matplotlib.use("Agg")


import torch

from torch.utils.data import DataLoader as torch_DataLoader
from torch_geometric.loader import DataLoader as torch_geometric_DataLoader
from torch.utils.data import Sampler
import datetime
from Extract_mesh.write_tec import write_tecplotzone


from dataset.Load_mesh import H5CFDdataset, CFDdatasetBase

class Data_Pool:
    def __init__(self, params=None,device=None,state_save_dir=None,):
        self.params = params
        self.device = device
        
        try:
            if not (state_save_dir.find("traing_results") != -1):
                os.makedirs(f"{state_save_dir}/traing_results", exist_ok=True)
                self.state_save_dir = f"{state_save_dir}/traing_results"
        except:
            print(
                ">>>>>>>>>>>>>>>>>>>>>>>Warning, no state_save_dir is specified, check if traing states is specified<<<<<<<<<<<<<<<<<<<<<<<<<<<<<"
            )
        
        # 绘制被重置的这个case当前状态
        self._plot_env=True

        
    def _set_reset_env_flag(self, flag=False, rst_time=1):
        self.reset_env_flag = flag
        self.rst_time = rst_time

    def load_mesh_to_cpu(
        self,
        dataset_dir=None,
    ):
        
        valid_h5file_paths = []
        for subdir, _, files in os.walk(dataset_dir):
            for data_name in files:
                if data_name.endswith(".h5"):
                    valid_h5file_paths.append(os.path.join(subdir, data_name))

        mesh_dataset = H5CFDdataset(
            params=self.params, file_list=valid_h5file_paths
        )

        mesh_loader = torch_DataLoader(
            mesh_dataset,
            batch_size=4,
            num_workers=4,
            pin_memory=False,
            collate_fn=lambda x: x,
        )

        print("loading whole dataset to cpu")
        self.meta_pool = []
        self.q_pool = []
        start_idx = 0
        while True:
            for _, trajs in enumerate(mesh_loader):
                tmp = list(trajs)
                for meta_data, init_q in tmp:
                    meta_data["global_idx"] = torch.arange(start_idx,start_idx+init_q.shape[0])
                    self.meta_pool.append(meta_data)
                    self.q_pool.append(init_q.to(torch.float64))
                    start_idx += init_q.shape[0]
 
                    if len(self.meta_pool)>=self.params.dataset_size:
                        break
                    
            if len(self.meta_pool)>=self.params.dataset_size:
                break
            
        self.q_pool = torch.cat(self.q_pool, dim=0)

        self.dataset_size = len(self.meta_pool)
        self.params.dataset_size = self.dataset_size
        

        
        # 控制画图个数的文件夹分组
        self.plot_count = 0
        return self.dataset_size, self.params
    
    @staticmethod
    def datapreprocessing(
        graph_node
    ):
        senders, receivers = graph_node.edge_index
  
      

        # permute edge direction
        releative_mesh_pos = (
            torch.index_select(graph_node.pos, 0, senders)
            - torch.index_select(graph_node.pos, 0, receivers)
        ).to(torch.float32)

        edge_length = torch.norm(releative_mesh_pos, dim=1, keepdim=True)

    

        graph_node.edge_attr = torch.cat(
            (releative_mesh_pos, edge_length), dim=1
        )


        return graph_node
    
    def reset_env(self, plot=False):

        # 弹出第0个网格的mesh数据
        old_mesh = self.meta_pool.pop(0)
        old_global_idx = old_mesh["global_idx"]
        
        # 绘图
        if plot:
            uvp_node = self.uvp_node_pool[old_global_idx]
            
            
            self.export_to_tecplot(old_mesh, uvp_node, datalocation="node")
        
            
            self._plot_env = False

        # 移除属于第0个网格的uvp数据
     
        self.q_pool = self.q_pool[old_global_idx.shape[0]:] 

        
        for iidx in range(len(self.meta_pool)):
            cur_meta_data = self.meta_pool[iidx]
            cur_meta_data["global_idx"] -= old_global_idx.shape[0]

        # 接着生成新的网格数据，即重新选一个边界条件
        new_mesh, init_q = CFDdatasetBase.transform_mesh(
            old_mesh, 
            self.params
        )
        new_mesh["global_idx"] = torch.arange(
            self.q_pool.shape[0], self.q_pool.shape[0]+init_q.shape[0]
        )
        self.q_pool = torch.cat((self.q_pool, init_q), dim=0)

        self.meta_pool.append(new_mesh)

    def export_to_tecplot(self, mesh, q, datalocation="cell", file_name=None):
        
        case_name = mesh["case_name"]
        dt = mesh["dtau"].squeeze().item()
        source = mesh["source"].squeeze().item()
        aoa = mesh["aoa"].squeeze().item()
        to_numpy = lambda x: x.cpu().numpy() if x.is_cuda else x.numpy()
        write_dataset = []
        interior_zone_numpy = {}
        boundary_zone_numpy = {}    

        interior_zone_numpy["velocity"] = q[:,0:2].numpy()
        interior_zone_numpy["pressure"] = q[:,2:3].numpy()
        interior_zone_numpy["density"] = q[:,3:4].numpy()
        interior_zone_numpy["mesh_pos"] = mesh["mesh_pos"].unsqueeze(0).float().numpy()
        interior_zone_numpy['cells'] = mesh['cells_node']
        interior_zone_numpy['cells_index'] = mesh['cells_index']
        # interior_zone_numpy['face_node'] = mesh["edge_index"]  
        interior_zone_numpy['zonename'] = 'Fluid'
        interior_zone_numpy["data_packing_type"] = datalocation
        for k, v in mesh.items():
            if isinstance(v, torch.Tensor):
                interior_zone_numpy[k] = to_numpy(v)
            else:
                interior_zone_numpy[k] = v
        write_dataset.append(interior_zone_numpy)              


        if "boundary_zone" in mesh:
    
            boundary_zone = mesh["boundary_zone"]
            for k, v in boundary_zone.items():
                if not isinstance(v, torch.Tensor):
                    continue
                else:
                    boundary_zone_numpy[k] = to_numpy(v)
            mask_node_boundary = boundary_zone["mask_node_boundary"]
            boundary_zone_numpy["velocity"] = uv_uns[:,mask_node_boundary,:].numpy()
            boundary_zone_numpy["pressure"] = p_uns[:,mask_node_boundary,:].numpy()
            boundary_zone_numpy["data_packing_type"] = "node"
            boundary_zone_numpy['zonename'] = 'BOUNDARY'
            write_dataset.append(boundary_zone_numpy)

        try:
            Re=mesh["Re"].squeeze().item()
        except:
            Re=0
            Warning("No Re number in the mesh set to 0")

        
        if file_name is None:
        
            save_dir_num = self.plot_count//50
            saving_dir = f"{self.state_save_dir}/{save_dir_num*50}-{(save_dir_num+1)*50}"
            os.makedirs(saving_dir, exist_ok=True)
            saving_path = f"{saving_dir}/NO.{self.plot_count}_{case_name}_Re={Re:.2f}_dtau={dt:.3f}_source={source:.2f}_aoa={aoa:.2f}.dat"
        else:
            saving_path = file_name

        write_tecplotzone(
            filename=saving_path,
            datasets=write_dataset,
            time_step_length=1,
        )
        



   
        
        self.plot_count+=1

    def update_env(self, mesh):
        
        mesh["time_steps"] += 1

        if "wave" in mesh["flow_type"]:
            (
                mesh,
                theta_PDE,
                sigma,
                source_pressure_node,
            ) = CFDdatasetBase.set_Wave_case(
                mesh,
                self.params,
                mesh["mean_u"].item(),
                mesh["rho"].item(),
                mesh["mu"].item(),
                mesh["source"].item(),
                mesh["aoa"].item(),
                mesh["dt"].item(),
                mesh["source_frequency"].item(),
                mesh["source_strength"].item(),
                time_index=mesh["time_steps"],
            )
            mesh["theta_PDE"] = theta_PDE
            mesh["sigma"] = sigma
            mesh["wave_uvp_on_node"][0, :, 2:3] += source_pressure_node

            return mesh

        else: 

            mesh = CFDdatasetBase.To_Cartesian(mesh,resultion=(300,100))

        return mesh

    def payback(self, q, global_idx):
        
        # update uvp pool
        self.q_pool[global_idx,0:4] = q.data
        
        
        if self.reset_env_flag:
            for _ in range(self.rst_time):
                
                # 每次都将第0个网格重置，然后生成新网格append到pool尾部
                self.reset_env(plot=self._plot_env)
                
            self.reset_env_flag=False    
            self._plot_env = True

    def payback_for_vis(self, q, global_idx,num_graph,batch):
        
        # update uvp pool
        self.q_pool[global_idx,0:4] = q.data.data
        
        for i in range(num_graph):

            sample_mask = torch.where(batch==i)[0]

            self.export_to_tecplot(self.meta_pool[i], self.q_pool[sample_mask], datalocation="cell")


        
class CustomGraphData(Data):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

    def __inc__(self, key, value, *args, **kwargs):
        offset_rules = {
            "edge_index": self.num_nodes,
            "face": self.num_nodes,
            "cells_node": self.num_nodes,
            "face_node": self.num_nodes,
            "cells_face": self.num_nodes,
            "neighbour_cell": self.num_nodes,
            "face_node_x": self.num_nodes,
            "support_edge": self.num_nodes,
            "periodic_idx": self.num_nodes,
            "init_loss":0,
            "case_name":0,
            "query": 0,
            "grids": 0,
            "pos": 0,
            "A_node_to_node": 0,
            "A_node_to_node_x": 0,
            "B_node_to_node": 0,
            "B_node_to_node_x": 0,
            "cells_area": 0,
            "node_type": 0,
            "graph_index": 0,
            "theta_PDE": 0,
            "sigma": 0,
            "uvp_dim": 0,
            "dt_graph": 0,
            "x": 0,
            "y": 0,
            "m_ids": 0,
            "m_gs": 0,
            "global_idx": 0,
        }
        return offset_rules.get(key, super().__inc__(key, value, *args, **kwargs))

    def __cat_dim__(self, key, value, *args, **kwargs):
        cat_dim_rules = {
            "x": 0,
            "pos": 0,
            "y": 0,
            "norm_y": 0,
            "query": 0,  # 保持query为列表，不进行拼接
            "grids": 0,  # 保持query为列表，不进行拼接
            "edge_index": 1,  # edge_index保持默认的offset拼接
            "face":0,
            "voxel": 0,
            "init_loss":0,
            "support_edge":1,
            "graph_index": 0,
            "global_idx": 0,
            "periodic_idx": 1,
        }
        return cat_dim_rules.get(key, super().__cat_dim__(key, value, *args, **kwargs))
    
class GraphNodeDataset(InMemoryDataset):
    def __init__(self, base_dataset):
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        # 这里你可以根据需要从基类的pool中筛选出GraphNode的数据
        return self.base_dataset.meta_pool
    
    def len(self):
        return len(self.pool)

    def get(self, idx):
        minibatch_data = self.pool[idx]
        
        mesh_pos = minibatch_data["mesh_pos"].to(torch.float64)

   
        cell_node = minibatch_data["cells_node"].to(torch.long)

        graph_node = CustomGraphData(x=mesh_pos,
        
            face = cell_node,

            pos=mesh_pos,
   
     
            graph_index=torch.tensor([idx],dtype=torch.long),
        )

        return graph_node




class Graph_INDEX_Dataset(InMemoryDataset):
    def __init__(self, base_dataset):
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        # 这里你可以根据需要从基类的pool中筛选出GraphNode的数据
        return self.base_dataset.meta_pool
    
    @property
    def params(self):
        return self.base_dataset.params

    def len(self):
        return len(self.pool)

    def get(self, idx):
        minibatch_data = self.pool[idx]
      
        theta_PDE = minibatch_data["theta_PDE"].to(torch.float64)
        sigma = minibatch_data["sigma"].to(torch.float64)
        q_dim = minibatch_data["q_dim"].to(torch.float64)
        dtau= minibatch_data["dtau"].to(torch.float64)
 
        relaxtion = torch.tensor([minibatch_data["solving_params"]["relaxtion"]]).reshape(-1,1).to(torch.float64)
        
        graph_Index = CustomGraphData(
            x=torch.tensor([idx],dtype=torch.long),
            pde_theta=theta_PDE,
            sigma=sigma,
            q_dim=q_dim,
            dtau = dtau,
            relaxtion = relaxtion,
            graph_index=torch.tensor([idx],dtype=torch.long),
        )

        return graph_Index
    

class GraphCellDataset(InMemoryDataset):
    
    def __init__(self, base_dataset):
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        # 这里你可以根据需要从基类的pool中筛选出GraphNode的数据
        return self.base_dataset.meta_pool

    def len(self):
        return len(self.pool)

    def get(self, idx):
        
        minibatch_data = self.pool[idx]        
        """Optional node attr"""
        map_index = minibatch_data["map_index"].to(torch.long)
        cell_pos = minibatch_data['cell_pos'].to(torch.float64)
        area = minibatch_data['area'].to(torch.float64)
        case_name = minibatch_data["case_name"]
        global_idx = minibatch_data["global_idx"].long()
        q_node = self.base_dataset.q_pool[global_idx]
        neighbor_cell_index = minibatch_data['neighbor_cell_index'].to(torch.long)
        graph_cell = Data(x=q_node, 
                  
                        face = neighbor_cell_index,
                        map_index = map_index,
                        pos=cell_pos,
                        area = area,
                        global_idx=global_idx,
                        case_name=torch.tensor([ord(char) for char in (case_name)], dtype=torch.long),
                        graph_index= torch.as_tensor([idx])) 
        return graph_cell
    

class GraphEdgeDataset(InMemoryDataset):
    
    def __init__(self, base_dataset):
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        # 这里你可以根据需要从基类的pool中筛选出GraphNode的数据
        return self.base_dataset.meta_pool

    def len(self):
        return len(self.pool)

    def get(self, idx):
        
        minibatch_data = self.pool[idx]
        target_q = minibatch_data["target|q"]
        
        du_face_index = minibatch_data['du_face_index'].to(torch.long)
        lr_face_index = minibatch_data['lr_face_index'].to(torch.long)
        face_interpolate_flag = minibatch_data['face_interpolate_flag'].to(torch.bool)
        metrics = minibatch_data['face_metrics'].to(torch.float64)
        graph_edge = Data(x=metrics,
                          du_face=du_face_index,
                          lr_face = lr_face_index,
                          face_type = minibatch_data['face_type'].to(torch.long),
                          face_interpolate_flag = face_interpolate_flag,
                          y = target_q,
                          graph_index= torch.as_tensor([idx]),
                         )
        
        return graph_edge



class GraphBlockCellDataset(InMemoryDataset):
    def __init__(self, base_dataset):
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        # 这里你可以根据需要从基类的pool中筛选出GraphNode的数据
        return self.base_dataset.meta_pool

    def len(self):
        return len(self.pool)

    def get(self, idx):
        minibatch_data = self.pool[idx]        
        # current_time_steps = torch.as_tensor([minibatch_data['time_steps']]).to(torch.long)

        # cell_attr
        tmp = minibatch_data['block_cells_node'].to(torch.long)[:,0:1]
        neighbor_cell_xi = minibatch_data['neighbor_cell_xi'].to(torch.long)
        neighbor_cell_eta = minibatch_data['neighbor_cell_eta'].to(torch.long)

        graph_block_cell = Data(x = tmp,
                        xi_cell_index=neighbor_cell_xi.T,
                        eta_cell_index = neighbor_cell_eta.T,
                        graph_index= torch.as_tensor([idx]),
          )
        
        return graph_block_cell 

class GraphMapDataset(InMemoryDataset):
    
    def __init__(self, base_dataset):
        super().__init__()
        self.base_dataset = base_dataset

    @property
    def pool(self):
        # 这里你可以根据需要从基类的pool中筛选出GraphNode的数据
        return self.base_dataset.meta_pool

    def len(self):
        return len(self.pool)

    def get(self, idx):
        
        minibatch_data = self.pool[idx]        
        """Optional node attr"""

        lr_cell_to_face = minibatch_data['lr_cell_to_face'].to(torch.long)
        du_cell_to_face = minibatch_data['du_cell_to_face'].to(torch.long)
        lr_cell_to_cell = minibatch_data['lr_cell_to_cell'].to(torch.long)
        du_cell_to_cell = minibatch_data['du_cell_to_cell'].to(torch.long)
        dummy_pos = minibatch_data['dummy_pos'].to(torch.float64)
    
        graph_map = Data(x = dummy_pos,
                       
                        to_face_lr_cell_index = lr_cell_to_face,
                        to_cell_lr_cell_index = lr_cell_to_cell,
                        to_face_du_cell_index  = du_cell_to_face,
                        to_cell_du_cell_index = du_cell_to_cell,
                 
                        graph_index= torch.as_tensor([idx])) 
        return graph_map

class SharedSampler(Sampler):
    def __init__(self, data_source):
        self.data_source = data_source
        self.epoch = 0
        self.specific_indices = None  # 用于存储特定的索引

    def __iter__(self):
        g = torch.Generator()
        g.manual_seed(self.epoch)
        if self.specific_indices is not None:
            return iter(self.specific_indices)
        return iter(torch.randperm(len(self.data_source), generator=g).tolist())

    def __len__(self):
        return len(self.data_source)

    def set_epoch(self, epoch):
        self.epoch = int(datetime.datetime.now().timestamp())

    def set_specific_indices(self, indices):
        self.specific_indices = indices

class CustomDataLoader:
    def __init__(
        self,

        graph_edge_dataset,

        graph_cell_dataset,
        graph_map_dataset,
        graph_Index_dataset,
        batch_size,
        sampler,
        num_workers=4,
        pin_memory=False,
    ):
        # 保存输入参数到实例变量

        self.graph_edge_dataset = graph_edge_dataset
 
        self.graph_cell_dataset = graph_cell_dataset
        self.graph_map_dataset = graph_map_dataset
        self.graph_Index_dataset = graph_Index_dataset
        self.batch_size = batch_size
        self.sampler = sampler
        self.num_workers = num_workers
        self.pin_memory = pin_memory

        # 初始化DataLoaders
        # self.loader_A = torch_geometric_DataLoader(
        #     graph_node_dataset,
        #     batch_size,
        #     sampler=sampler,
        #     num_workers=num_workers,
        #     pin_memory=pin_memory,
        # )
        self.loader_B = torch_geometric_DataLoader(
            graph_edge_dataset,
            batch_size,
            sampler=sampler,
            num_workers=num_workers,
            pin_memory=pin_memory,
        )

        self.loader_D = torch_geometric_DataLoader(
            graph_cell_dataset,
            batch_size,
            sampler=sampler,
            num_workers=num_workers,
            pin_memory=pin_memory,
        )
        self.loader_F= torch_geometric_DataLoader(
            graph_Index_dataset,
            batch_size,
            sampler=sampler,
            num_workers=num_workers,
            pin_memory=pin_memory,
        )
        self.loader_E = torch_geometric_DataLoader(
            graph_map_dataset,
            batch_size,
            sampler=sampler,
            num_workers=num_workers,
            pin_memory=pin_memory,
        )

    def __iter__(self):
        return zip(
            self.loader_B, self.loader_D, self.loader_E,self.loader_F
        )

    def __len__(self):
        return min(
    
            len(self.loader_B),
            len(self.loader_D),
            len(self.loader_E),
            len(self.loader_F),
        )

    def get_specific_data(self, indices):
        # 设置Sampler的特定索引
        self.sampler.set_specific_indices(indices)

        # # 重新创建DataLoaders来使用更新的Sampler
        # self.loader_A = torch_geometric_DataLoader(
        #     self.graph_node_dataset,
        #     self.batch_size,
        #     sampler=self.sampler,
        #     num_workers=self.num_workers,
        #     pin_memory=self.pin_memory,
        # )
        self.loader_B = torch_geometric_DataLoader(
            self.graph_edge_dataset,
            self.batch_size,
            sampler=self.sampler,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
        )

        self.loader_D = torch_geometric_DataLoader(
            self.graph_cell_dataset,
            self.batch_size,
            sampler=self.sampler,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
        )
        self.loader_F = torch_geometric_DataLoader(
            self.graph_Index_dataset,
            self.batch_size,
            sampler=self.sampler,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
        )
        self.loader_E = torch_geometric_DataLoader(
            self.graph_map_dataset,
            self.batch_size,
            sampler=self.sampler,
            num_workers=self.num_workers,
            pin_memory=self.pin_memory,
        )

        graph_edge, graph_cell, graph_map, graph_Index = next(
            iter(self)
        )

        minibatch_data = self.graph_node_dataset.pool[indices[0]]

        origin_mesh_path = "".join(
            [chr(int(f)) for f in minibatch_data["origin_mesh_path"][0, :, 0].numpy()]
        )

        flow_type = minibatch_data["flow_type"]
        if ("cavity" in flow_type) or ("possion" in flow_type):
            has_boundary = False
        else:
            has_boundary = True

        return (
  
            graph_edge,
  
            graph_cell,
            graph_map,
            graph_Index,
            has_boundary,
            origin_mesh_path,
        )

class DatasetFactory:
    def __init__(
        self,
        params=None,
        dataset_dir=None,
        state_save_dir=None,
        device=None,
    ):
        self.base_dataset = Data_Pool(
            params=params,
            device=device,
            state_save_dir=state_save_dir,
        )

        self.dataset_size, self.params = self.base_dataset.load_mesh_to_cpu(
            dataset_dir=dataset_dir,
        )

    def create_datasets(self, batch_size=100, num_workers=4, pin_memory=True):

        graph_edge_dataset = GraphEdgeDataset(base_dataset=self.base_dataset)
        graph_cell_dataset = GraphCellDataset(base_dataset=self.base_dataset)
        graph_map_dataset = GraphMapDataset(base_dataset=self.base_dataset)
        graph_Index_dataset = Graph_INDEX_Dataset(base_dataset=self.base_dataset)

        # 创建SharedSampler并将其传递给CustomDataLoader

        sampler = SharedSampler(graph_cell_dataset)

        loader = CustomDataLoader(
            graph_edge_dataset,
            graph_cell_dataset,
            graph_map_dataset,
            graph_Index_dataset,
            batch_size=batch_size,
            sampler=sampler,
            num_workers=num_workers,
            pin_memory=pin_memory,
        )

        return self.base_dataset, loader, sampler
