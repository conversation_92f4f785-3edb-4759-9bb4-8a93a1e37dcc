.vscode
Extract_mesh/__pycache__
dataset/__pycache__
FVMmodel/__pycache__
Logger
Extract_mesh/__pycache__/__init__.cpython-311.pyc
Extract_mesh/__pycache__/parse_tfrecord_refactor.cpython-311.pyc
Extract_mesh/__pycache__/write_tec.cpython-311.pyc
FVMmodel/__pycache__
utils/__pycache__
Logger
dataset/__pycache__
Extract_mesh/__pycache__
utils/__pycache__
FVMmodel/__pycache__
train_handler.txt
train_log.txt
run_train.sh
FVMmodel/GNN
FVMmodel/Transolver
FVMmodel/Unet
requirements.txt
residuals.dat
/home/<USER>/Mycode/GN-unsteady/Logger
dataset/__pycache__
requirements.txt
residuals.dat
train_log.txt
train_handler.txt
ref/__pycache__
ref/__init__.py
models/__pycache__