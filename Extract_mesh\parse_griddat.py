import sys
import os
file_dir = os.path.dirname(os.path.dirname(__file__))
sys.path.append(file_dir)
import numpy as np
import multiprocessing
import torch

from Extract_mesh.to_h5 import (
    extract_mesh_state,
    NodeType,
)
import os
import h5py
import sys
import torch.nn.functional as F
from utils.utilities import *
from torch_scatter import scatter_mean

# 将输出缓冲区设置为0
sys.stdout.flush()


class structured_grid():
    def __init__(self,mesh_file=None,data_file=None,file_dir=None,case_name=None,path=None):
        self.path = path
        self.mesh_file_path = mesh_file
        
        self.read_otype_grid()

    def write_tec(self,prepath):
        M,N =self.row_col 
        tecplot_file = f'{prepath}.dat' 
        with open(tecplot_file, 'w') as f:
            f.write('TITLE = "Field Data"\n')
            f.write('VARIABLES = "X", "Y"\n')

            f.write('ZONE T="Zone1", N=%d, E=%d, F=FEPOINT, ET=QUADRILATERAL\n' % (M*N,(M-1)*(N-1)))
            f.write(' '+'\n')
            for i in range(M*N):
                    f.write(str(self.mesh_pos.reshape(-1,2)[i, 0])+' '+str(self.mesh_pos.reshape(-1,2)[i, 1])+'\n')
            f.write(' '+'\n')
            for i in range((M-1)*(N-1)):
                
                    f.write(str(self.cells_node.reshape(-1,4)[i, 0]+1)+' '+str(self.cells_node.reshape(-1,4)[i, 1]+1)+' '+str(self.cells_node.reshape(-1,4)[i, 2]+1)+' '+str(self.cells_node.reshape(-1,4)[i, 3]+1)+'\n')

    def read_otype_grid(self):

        if "NNfoil" in self.mesh_file_path:
            xxyy = np.loadtxt(self.mesh_file_path, skiprows=1)
            xx = xxyy[0:round(xxyy.shape[0]/2)].T
            yy = xxyy[round(xxyy.shape[0]/2):].T
            xx = np.r_[xx, xx[0:1]]
            yy = np.r_[yy, yy[0:1]]

        else:
            data = np.fromfile(self.mesh_file_path, sep=' ')

            I = int(data[0]); J = int(data[1])
            xxyy = data[2:].reshape(2*J, I)
            xx = xxyy[:J,:].T; yy = xxyy[J:,:].T

        row, col = xx.shape[0], xx.shape[1]

        self.mesh_pos =(np.concatenate([xx.reshape(-1,1),yy.reshape(-1,1)],axis=1))

        node_index_unpad = np.arange(row*col).reshape(row,col)
        block = [torch.from_numpy(node_index_unpad.reshape(1,1,row,col)).float()]
      
        self.row_col = (row, col)

        self.cells_node = self.get_cells_node(block).long().numpy().reshape(-1)

        self.cells_index =(np.arange((row-1)*(col-1)))[:, None].repeat(axis=1, repeats=4).reshape(-1)
                
        self.mesh_pos_cell = scatter_mean(torch.from_numpy(self.mesh_pos[self.cells_node]),torch.from_numpy(self.cells_index),dim=0).numpy()

        num_cells = (row-1)*(col-1)
    
     
        cell_index_unpad = np.arange(num_cells).reshape(row-1,col-1)

        cell_index_pad = np.pad(cell_index_unpad.reshape(row-1,col-1), ((2, 2), (2, 2)), mode='edge')

        mesh_pos_reshape = self.mesh_pos.reshape(row,col,2)

        dummy_pos = self.extend_pos(self.mesh_pos_cell.reshape(row-1,col-1,2)).reshape(-1,2)

        self.dummy_pos = self.extend_pos(dummy_pos.reshape(row+1,col+1,2)).reshape(-1,2)

        xi_face_interpolate_flag = np.zeros((row,col-1),dtype=bool)
        eta_face_interpolate_flag = np.zeros((row-1,col),dtype=bool)

        xi_face_type = np.empty((row,col-1))
        eta_face_type = np.empty((row-1,col))







        if np.array_equal(mesh_pos_reshape[0, :], mesh_pos_reshape[-1, :]):
            cell_index_pad[0,2:-2] = cell_index_unpad[-2,:]
            cell_index_pad[1,2:-2] = cell_index_unpad[-1,:]
            cell_index_pad[-2,2:-2] = cell_index_unpad[0,:]
            cell_index_pad[-1,2:-2] = cell_index_unpad[1,:]

            eta_face_interpolate_flag[:,-1] = True
            eta_face_interpolate_flag[:,1] =True
            

            if np.abs(mesh_pos_reshape[:, 0,1]).sum() > np.abs(mesh_pos_reshape[:, -1,1]).sum():

                wall_location = "R"
                eta_face_type[:,0] = NodeType.FARFIELD
                eta_face_type[:,-1] = NodeType.WALL_BOUNDARY
                xi_face_type[:,:] = NodeType.NORMAL
                eta_face_type[:,1:-1] = NodeType.NORMAL
                eta_face_type[:,1] = NodeType.NEAR_BOUNDARY
                eta_face_type[:,-2] = NodeType.NEAR_BOUNDARY


       
                
            else:

                wall_location = "L"
      
                eta_face_type[:,-1] = NodeType.FARFIELD
                eta_face_type[:,0] = NodeType.WALL_BOUNDARY
                xi_face_type[:,:] = NodeType.NORMAL
                eta_face_type[:,1:-1] = NodeType.NORMAL    
                eta_face_type[:,1] = NodeType.NEAR_BOUNDARY
                eta_face_type[:,-2] = NodeType.NEAR_BOUNDARY   

                

        elif np.array_equal(mesh_pos_reshape[:, 0], mesh_pos_reshape[:, -1]): 
            cell_index_pad[2:-2,0] = cell_index_unpad[:,-2]
            cell_index_pad[2:-2,1] = cell_index_unpad[:,-1]
            cell_index_pad[2:-2,-2] = cell_index_unpad[:,0]
            cell_index_pad[2:-2,-1] = cell_index_unpad[:,1]

            xi_face_interpolate_flag[1,:] = True
            xi_face_interpolate_flag[-1,:] = True

            if np.abs(mesh_pos_reshape[0, :,1]).sum() > np.abs(mesh_pos_reshape[-1, :,1]).sum():

                wall_location = "D"

                xi_face_type[-1,:] = NodeType.WALL_BOUNDARY
                xi_face_type[0,:] = NodeType.FARFIELD
                eta_face_type[:,:] = NodeType.NORMAL
                xi_face_type[1:-1,:] = NodeType.NORMAL
                xi_face_type[1,:] = NodeType.NEAR_BOUNDARY
                xi_face_type[-2,:] = NodeType.NEAR_BOUNDARY

 

            else:

                wall_location = "U"

                xi_face_type[-1,:] = NodeType.FARFIELD
                xi_face_type[0,:] = NodeType.WALL_BOUNDARY
                eta_face_type[:,:] = NodeType.NORMAL
                xi_face_type[1:-1,:] = NodeType.NORMAL            
                xi_face_type[1,:] = NodeType.NEAR_BOUNDARY
                xi_face_type[-2,:] = NodeType.NEAR_BOUNDARY              

        xi_face_metrics,eta_face_metrics,self.area = self.cal_metrics(self.mesh_pos.reshape(-1,2),row,col)

        self.face_metrics = np.concatenate((xi_face_metrics,eta_face_metrics),axis=0)

        self.face_type = np.concatenate((xi_face_type.reshape(-1),eta_face_type.reshape(-1)),axis=0)

        self.face_interpolate_flag = np.concatenate((xi_face_interpolate_flag.reshape(-1),eta_face_interpolate_flag.reshape(-1)),axis=0)

        xi_face_centers,eta_face_centers = 0.5 * (mesh_pos_reshape[:, :-1, :] + mesh_pos_reshape[:, 1:, :]),0.5 * (mesh_pos_reshape[:-1, :, :] + mesh_pos_reshape[1:, :, :])

        self.face_centers = np.concatenate((xi_face_centers.reshape(-1,2),eta_face_centers.reshape(-1,2)),axis=0)

        self.neighbor_cell_index = gen_neighbor_cell_flattened(cell_index_unpad)

        self.map_index = cell_index_pad.reshape(-1)

        num_map_cells = np.arange(self.map_index.shape[0])

        ll_cell_to_face  = muscl_2nd_ww(num_map_cells.reshape(row+3,col+3)[2:-2,:])

        l_cell_to_face  = muscl_2nd_w(num_map_cells.reshape(row+3,col+3)[2:-2,:])

        rr_cell_to_face  = muscl_2nd_ee(num_map_cells.reshape(row+3,col+3)[2:-2,:])

        r_cell_to_face  = muscl_2nd_e(num_map_cells.reshape(row+3,col+3)[2:-2,:])

        uu_cell_to_face  = muscl_2nd_nn(num_map_cells.reshape(row+3,col+3)[:,2:-2])

        u_cell_to_face  = muscl_2nd_n(num_map_cells.reshape(row+3,col+3)[:,2:-2])

        dd_cell_to_face  = muscl_2nd_ss(num_map_cells.reshape(row+3,col+3)[:,2:-2])

        d_cell_to_face  = muscl_2nd_s(num_map_cells.reshape(row+3,col+3)[:,2:-2])

        u_cell_to_cell = get_up_node(num_map_cells.reshape(row+3,col+3)[1:-1,2:-2])

        l_cell_to_cell = get_left_node(num_map_cells.reshape(row+3,col+3)[2:-2,1:-1])

        r_cell_to_cell = get_right_node(num_map_cells.reshape(row+3,col+3)[2:-2,1:-1])

        d_cell_to_cell = get_down_node(num_map_cells.reshape(row+3,col+3)[1:-1,2:-2])

        self.lr_cell_to_cell = np.stack((l_cell_to_cell,r_cell_to_cell),axis=0)

        self.du_cell_to_cell = np.stack((u_cell_to_cell,d_cell_to_cell),axis=0)
        
        self.lr_cell_to_face = np.stack((ll_cell_to_face,l_cell_to_face,r_cell_to_face,rr_cell_to_face),axis=0)

        self.du_cell_to_face = np.stack((uu_cell_to_face,u_cell_to_face,d_cell_to_face,dd_cell_to_face),axis=0)

        lr_face_index = np.arange(self.lr_cell_to_face.shape[1])

        du_face_index = np.arange(self.du_cell_to_face.shape[1])

        l_face  = get_left(lr_face_index.reshape(row-1,col))

        r_face  = get_right(lr_face_index.reshape(row-1,col))

        d_face  = get_down(du_face_index.reshape(row,col-1))

        u_face  = get_up(du_face_index.reshape(row,col-1))

        self.lr_face_index = (np.stack((r_face,l_face),axis=1)+du_face_index.shape[0]).T

        self.du_face_index = (np.stack((d_face,u_face),axis=1)).T

   



    def get_cells_node(self,block_index_list):
        cells_node  = []
        for id,i in enumerate(block_index_list):
            mask = i
            cells_node_0 = get_id1(mask)
            cells_node_1 = get_id2(mask)
            cells_node_2 = get_id3(mask)
            cells_node_3 = get_id4(mask)

            block_cells_node = torch.stack((cells_node_0,cells_node_1,cells_node_2,cells_node_3),dim=-1).reshape(-1,4)
            cells_node.append(block_cells_node)
        return torch.cat(cells_node,dim=0)
    

    def cal_metrics(self,pos,row,col):
        """
        基于calc_normal和calc_area的向量化实现
        直接计算面法向量和单元面积，不预先初始化固定shape
        """
        X = pos[:,0].reshape(row,col)
        Y = pos[:,1].reshape(row,col)
        
        # 计算沿ξ方向边界的法向量 (dni)
        # 只计算有效的边界 (j从1到my-1)
        dni_x = Y[:, 1:] - Y[:, :-1]
        dni_y = -(X[:, 1:] - X[:, :-1])
        dni = np.sqrt(dni_x**2 + dni_y**2)
        
        # 计算沿η方向边界的法向量 (dnj)
        # 只计算有效的边界 (i从1到mx-1)
        dnj_x = Y[:-1, :] - Y[1:, :]
        dnj_y = -(X[:-1, :] - X[1:, :])
        dnj = np.sqrt(dnj_x**2 + dnj_y**2)
        
        # 计算单元面积
        # 只计算内部单元 (i从1到mx-1, j从1到my-1)
        dxac = X[1:, 1:] - X[:-1, :-1]
        dyac = Y[1:, 1:] - Y[:-1, :-1]
        dxbd = X[:-1, 1:] - X[1:, :-1]
        dybd = Y[:-1, 1:] - Y[1:, :-1]
        area = 0.5 * np.abs(dxac * dybd - dxbd * dyac)
        
        # 返回所有计算结果，shape由实际计算决定
        return np.concatenate((dni_x.reshape(-1,1), dni_y.reshape(-1,1), dni.reshape(-1,1)),axis=-1),np.concatenate((dnj_x.reshape(-1,1), dnj_y.reshape(-1,1), dnj.reshape(-1,1)),axis=-1),np.maximum(area, 1e-30).reshape(-1,1)
    
    def construct_neighbor_cell(self,cells_index,cells_face):
            senders_cell = calc_node_centered_with_cell_attr(cell_attr=cells_index,
                                                            cells_node=cells_face,
                                                            cells_index=cells_index,
                                                            reduce="max",
                                                            map=False).squeeze(1)
            
            recivers_cell = calc_node_centered_with_cell_attr(cell_attr=cells_index,
                                                            cells_node=cells_face,
                                                            cells_index=cells_index,
                                                            reduce="min",
                                                            map=False).squeeze(1)
            neighbour_cell = torch.stack((recivers_cell,senders_cell),dim=0)
            return neighbour_cell
    
    def extend_pos(self,pos):
        #开始生成padded的坐标矩阵用于计算雅可比矩阵
        block_pos = pos
        block_pos_pad_a = np.pad(block_pos, ((1, 1), (1, 1),(0,0)), mode='reflect')
        block_pos_pad_b = np.pad(block_pos, ((1, 1), (1, 1),(0,0)), mode='edge')

        # 计算 A 最外层关于 B 最外层的对称点
        # 上边界和下边界
        block_pos_pad_a[0, :, :] = 2 * block_pos_pad_b[0, :, :] - block_pos_pad_a[0, :, :]
        block_pos_pad_a[-1, :, :] = 2 * block_pos_pad_b[-1, :, :] - block_pos_pad_a[-1, :, :]

        # 左边界和右边界
        block_pos_pad_a[1:-1, 0, :] = 2 * block_pos_pad_b[1:-1, 0, :] - block_pos_pad_a[1:-1, 0, :]
        block_pos_pad_a[1:-1, -1, :] = 2 * block_pos_pad_b[1:-1, -1, :] - block_pos_pad_a[1:-1, -1, :]

        return block_pos_pad_a    

   
    def extract_mesh(self,):


        mesh = {"mesh_pos":torch.from_numpy(self.mesh_pos),
                "cell_pos":torch.from_numpy(self.mesh_pos_cell),
                "face_pos":torch.from_numpy(self.face_centers),
                "dummy_pos":torch.from_numpy(self.dummy_pos),
                "face_type":torch.from_numpy(self.face_type).to(torch.long),
                "face_interpolate_flag":torch.from_numpy(self.face_interpolate_flag).to(torch.bool),
         
                "neighbor_cell_index":torch.from_numpy(self.neighbor_cell_index).to(torch.long),
                "lr_cell_to_cell":torch.from_numpy(self.lr_cell_to_cell).to(torch.long),
                "du_cell_to_cell":torch.from_numpy(self.du_cell_to_cell).to(torch.long),
                    "cells_node":torch.from_numpy(self.cells_node).squeeze(-1).to(torch.long),
                    "cells_index":torch.from_numpy(self.cells_index).to(torch.long),
                    "map_index":torch.from_numpy(self.map_index).to(torch.long),
                    "lr_cell_to_face":torch.from_numpy(self.lr_cell_to_face).to(torch.long),
                    "du_cell_to_face":torch.from_numpy(self.du_cell_to_face).to(torch.long),
                    "lr_face_index":torch.from_numpy(self.lr_face_index).to(torch.long),
                    "du_face_index":torch.from_numpy(self.du_face_index).to(torch.long),
                    "face_metrics":torch.from_numpy(self.face_metrics),
                    "area":torch.from_numpy(self.area),
                   }
                    


        h5_dataset = extract_mesh_state(
            mesh,
            path=self.path,
        )

        return h5_dataset






# Define the processing function
def process_file(plot, file_path, path, queue):
    
    file_name = os.path.basename(file_path)
    file_dir = os.path.dirname(file_path)
    case_name = os.path.basename(file_dir)
    path["file_dir"] = file_dir
    path["case_name"] = case_name
    path["file_name"] = file_name
    
    # start convert func
    if file_path.endswith(".x"):
        data = structured_grid(
            mesh_file=file_path,
            data_file=None,
            file_dir=file_dir,
            case_name=case_name,
            path=path,
        )

    else:
        return None

    h5_dataset = data.extract_mesh()

    # Put the results in the queue
    queue.put((h5_dataset, case_name, file_dir))


# Writer process function
def writer_process(queue, path):

    while True:

        # Get data from queue
        h5_data, case_name, file_dir = queue.get()
        
        # Break if None is received (sentinel value)
        if h5_data is None:
            break
        
        os.makedirs(file_dir, exist_ok=True)
        h5_writer = h5py.File(f"{file_dir}/{case_name}.h5", "w")

        # Write dataset key value
        group = h5_writer.create_group(case_name)
        for key, value in h5_data.items():
            if key in group:
                del group[key]
            group.create_dataset(key, data=value)

        print(f"{case_name} mesh has been writed")

    # 关闭所有的writer
    h5_writer.close()


if __name__ == "__main__":
    # for debugging

    debug_file_path = None
    # debug_file_path = "datasets/Tayler-Green/mesh.mphtxt"


    path = {
            "simulator": "???",
            "gird_path": "./grid_example/NACA0012_from_NNfoil",
            "mesh_only": True,
        }

    # stastic total number of data samples
    total_samples = 0
    file_paths = []
    for subdir, _, files in os.walk(path["gird_path"]):
        for data_name in files:
            if data_name.endswith(".x"):
                file_paths.append(os.path.join(subdir, data_name))

    # 统计选中的文件总数
    assert total_samples == 0, "Found no mesh files"
    total_samples = len(file_paths)
    print("total samples: ", total_samples)

    if debug_file_path is not None:
        multi_process = 1
    elif total_samples < multiprocessing.cpu_count():
        multi_process = total_samples
    else:
        multi_process = multiprocessing.cpu_count()

    # Start to convert data using multiprocessing
    global_data_index = 0
    with multiprocessing.Pool(multi_process) as pool:
        manager = multiprocessing.Manager()
        queue = manager.Queue()

        # Start writer process
        writer_proc = multiprocessing.Process(target=writer_process, args=(queue, path))
        writer_proc.start()

        if debug_file_path is not None:
            # for debuging
            results = process_file(
                        0,
                        debug_file_path,
                        path,
                        queue,
                    ),
        else:
            # Process files in parallel
            results = [
                pool.apply_async(
                    process_file,
                    args=(
                        file_index,
                        file_path,
                        path,
                        queue,
                    ),
                )
                for file_index, file_path in enumerate(file_paths)
            ]

            # Wait for all processing processes to finish
            for res in results:
                res.get()

        # Send sentinel value to terminate writer process
        queue.put((None, None, None))
        writer_proc.join()

    print("done")
