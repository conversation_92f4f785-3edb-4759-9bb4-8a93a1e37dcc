import torch
import argparse
from torch.optim import Adam
import torch_geometric.transforms as T
import numpy as np
from models.Meta_FDGN import Simulator
import os
from dataset import Load_mesh_old
from utils import get_param, utilities
from utils.utilities import extract_cylinder_boundary,extract_component
from utils.get_param import get_hyperparam
from utils.Logger import Logger, t_step
import time
from torch_geometric.data.batch import Batch
from torch_geometric.loader import DataLoader as torch_geometric_DataLoader
from torch_geometric.data import Data
from torch_geometric.nn import global_mean_pool,global_add_pool
from math import ceil
import matplotlib

matplotlib.use("Agg")
import matplotlib.pyplot as plt

import pandas as pd
from Extract_mesh import write_tec

import csv



def loss_function(x):
    return torch.pow(x, 2)


def plot_residuals(filename, rollout_index, re=None):
    # Load data
    data = pd.read_csv(filename)

    plt.figure(figsize=(10, 6))

    plt.semilogy(
        data["continuity_equation_residuals"],
        label="Continuity Equation Residuals",
        color="red",
    )
    plt.semilogy(
        data["x_momentum_residuals"], label="X Momentum Residuals", color="green"
    )
    plt.semilogy(
        data["y_momentum_residuals"], label="Y Momentum Residuals", color="blue"
    )

    plt.title("Residuals")
    plt.xlabel("Epoch")
    plt.ylabel("Residual")

    ax = plt.gca()  # Get the current axes instance
    ax.spines["top"].set_visible(False)
    ax.spines["right"].set_visible(False)

    plt.legend()
    plt.grid(True)
    plt.tight_layout()

    # Save and show plot
    saving_dir = os.path.dirname(filename)
    plt.savefig(
        f"{saving_dir}/re{str(re)}_mu{str(mu)}_{str(rollout_time_length)}steps_{rollout_index}_finetune({str(args.finetune)})_residuals.png",
        dpi=300,
    )
    plt.show()




def write_result(dataset=None,
    aoa=None,
    fluid_zone_list=None,
    result_dir=None,
    rollout_time_length=None,
    with_boundary=False,
    rho=None,
    mu=None,
    dt=None,
    whether_has_boundary=None,
    rollout_index=None,
):
    for fluid_zone_index, fluid_zone in enumerate(fluid_zone_list):
        graph_node = fluid_zone["graph_node"]

        saving_path = f"{result_dir}/NO.{rollout_index}_mean_u{mean_u}_rho_{rho}_mu_{mu}_dt_{dt}_{str(rollout_time_length)}steps_finetune({str(args.finetune)}).dat"

        # extrac interior zone
        interior_zone = {"zonename": "Fluid", "rho": rho, "mu": mu, "dt": dt}

        interior_zone["mesh_pos"] = fluid_zone["mesh_pos"][:,:, 0:2].to("cpu").numpy()

        interior_zone["velocity"] = (
            fluid_zone["predicted_node_uvp"][:, :, 0:2].to("cpu").numpy()
        )

        interior_zone["pressure"] = (
            fluid_zone["predicted_node_uvp"][:, :, 2:3].to("cpu").numpy()
        )
        interior_zone["cells_node"] = (
            graph_node.face.to("cpu").transpose(0, 1).unsqueeze(0).numpy()
        )
        interior_zone["face"] = (
            graph_node.edge_index.to("cpu").transpose(0, 1).unsqueeze(0).numpy()
        )

        interior_zone["vis_mask"] = fluid_zone["vis_mask"].cpu().numpy()
        interior_zone["cells_index"] = dataset["cells_index"]
        interior_zone["dt"] = dataset["dt"]
        interior_zone["data_packing_type"] = "node"



        if with_boundary and whether_has_boundary:
            fluid_zone["CL_list"] = []
            fluid_zone["CD_list"] = []
 
            boundary_zone = extract_cylinder_boundary(
                fluid_zone,
                aoa,
                dataset,
                graph_node,
                rho=rho,
                mu=mu,
                dt=dt,
            )
            component_zone = extract_component(fluid_zone,
                dataset,
                rho=rho,
                mu=mu,
                dt=dt,)


            write_zone = [interior_zone, boundary_zone,component_zone]

        else:
            write_zone = [interior_zone, None]

        file=os.path.join(os.path.dirname(saving_path), "force.dat")

        dt = interior_zone["dt"].cpu().numpy()
            
        with open(file, 'w') as f:
            f.write('Variables="time" "step" "Cd" "Cl"\n') 

            for t in range (len(fluid_zone["CL_list"] )):

                time=t*dt

                step = (t+1)


                Cd = fluid_zone["CD_list"][t]

                Cl = fluid_zone["CL_list"][t]

                f.write(f"{time} {step} {Cd} {Cl} \n")  

        write_tec.write_tecplotzone(
            saving_path,
            datasets=write_zone,
            time_step_length=interior_zone["velocity"].shape[0],
        )


def store_predicted_uvp(
    predicted_node_uvp=None,

    predicted_zone=None,
    graph_node=None,

    end=False,
    save_last=False,
):
    if  save_last:
        predicted_zone["predicted_node_uvp_list"].append(predicted_node_uvp)
        predicted_zone["vis_mask"].append(graph_node.vis_mask)
        predicted_zone["mesh_pos"].append(graph_node.pos)
        
    else:
        if end:
            predicted_zone["predicted_node_uvp_list"].append(predicted_node_uvp)
            predicted_zone["vis_mask"].append(graph_node.vis_mask)
            predicted_zone["mesh_pos"].append(graph_node.pos)
            

    if end:
        """
        shape: [batch_index,rollout_time_length,node/edge/cell_num,channal_num]
        """
        predicted_zone["predicted_node_uvp_list"] = torch.stack(
            predicted_zone["predicted_node_uvp_list"], dim=0
        )
        predicted_zone["vis_mask"] = torch.stack(
            predicted_zone["vis_mask"], dim=0
        )
        predicted_zone["mesh_pos"] = torch.stack(
            predicted_zone["mesh_pos"], dim=0
        )
        split_predicted_zone = []

        batch_node = graph_node.batch.to(predicted_node_uvp.device)


        num_graph = graph_node.num_graphs


        graph_node_sets = [graph_node]

        for i in range(num_graph):
            current_zone = {"zonename": "Fluid"}
            mask_node = batch_node == i

            current_zone["predicted_node_uvp"] = predicted_zone[
                "predicted_node_uvp_list"
            ][:, mask_node, :]

            current_zone["vis_mask"] = predicted_zone[
                "vis_mask"
            ][:, mask_node, :]

            current_zone["mesh_pos"] = predicted_zone[
                "mesh_pos"
            ][:, mask_node, :]

            current_zone["graph_node"] = graph_node_sets[i]


            split_predicted_zone.append(current_zone)

        predicted_zone = split_predicted_zone

    return predicted_zone


@torch.no_grad()
def rollout_without_fintune(
    fluid_model=None,
    datasets=None,
    rollout_index=0,
    result_dir=None,
    rollout_start_step=None,
    rollout_time_length=None,
    rho=None,
    mu=None,
    dt=None,
    optimizer=None,
    with_boundary=False,
    args=None,
):
    fluid_model.eval()
    predicted_zone = {
        "predicted_node_uvp_list": [],
        "vis_mask":[],
        "mesh_pos":[]

    }

    (
        graph_node,


        graph_edge_xi,
        graph_edge_eta,
        graph_block_cell,
        graph_map,
        graph_Index,
        whether_has_boundary,
        origin_mesh_file_location,
    ) = loader.get_specific_data([rollout_index])

    graph_node.x = datasets.total_uvp_node[graph_Index.case_global_index]

    (
        graph_node,


        graph_edge_xi,
        graph_edge_eta,
        graph_block_cell,
        graph_map,
        graph_Index
    ) = datasets.datapreprocessing_fd(
        graph_node=graph_node.to(device),

        graph_edge_xi= graph_edge_xi.to(device),
        graph_edge_eta= graph_edge_eta.to(device),
        graph_block_cell= graph_block_cell.to(device),
        graph_map= graph_map.to(device),
        graph_Index=graph_Index.to(device),
        dimless=params.dimless,
    )

    saving_dir = f"{result_dir}/mean_u{mean_u}_mu{str(mu)}_{str(rollout_time_length)}steps_finetune({str(args.finetune)})_{rollout_index}"

    os.makedirs(saving_dir, exist_ok=True)

    # do not reset the env during inference
    datasets._set_reset_env_flag(flag=False)

    # save origin mesh file path
    with open(f"{saving_dir}/origin_mesh_path.txt", "w") as mesh_path_file:
        mesh_path_file.write(origin_mesh_file_location[0])

    # resdiual monitor
    with open(
        f"{saving_dir}/NO.{rollout_index}_mean_u{mean_u}_rho_{rho}_mu_{mu}_dt_{dt}_{str(rollout_time_length)}steps_finetune({str(args.finetune)})_residuals.csv",
        "w",
        newline="",
    ) as file:
        writer = csv.writer(file)
        writer.writerow(
            [
                "continuity_equation_residuals",
                "x_momentum_residuals",
                "y_momentum_residuals",
            ]
        )
        start_time = time.time()
        datasets.alpha_list = [torch.tensor(0.0).to(torch.float32)]
        for epoch in range(rollout_start_step, rollout_time_length):
            last_iteration = epoch == rollout_time_length - 1

            # forwarding the model,graph_old`s cell and edge attr has been normalized but without model upadte
            (
                predicted_node_uvp,
                loss_cont,
                loss_momtentum_x,
                loss_momtentum_y,
     
            ) = fluid_model(
                graph_node=graph_node,
 
                graph_edge_xi = graph_edge_xi,
                graph_edge_eta = graph_edge_eta,
                graph_block_cell=graph_block_cell,
                graph_map=graph_map,
                graph_Index=graph_Index,
                params=params,
            )

            predicted_zone = store_predicted_uvp(
                predicted_node_uvp=predicted_node_uvp.detach(),
                predicted_zone=predicted_zone,
                graph_node=graph_node.detach(),
                end=last_iteration,
                save_last=args.save_last,
            )

            continuity_equation_residuals = abs(loss_cont.detach().mean().item())
            x_momentum_residuals = abs(loss_momtentum_x.detach().mean().item())
            y_momentum_residuals = abs(loss_momtentum_y.detach().mean().item())
        

            writer.writerow(
                [
                    continuity_equation_residuals,
                    x_momentum_residuals,
                    y_momentum_residuals,
                ]
            )

            if not last_iteration:
                # for key, tensor in graph_node.items():
                #     print(f"Key: {key}, Shape: {tensor.shape}")
  
                graph_node.x = predicted_node_uvp.detach()
                (
                    graph_node,

  
                    graph_edge_xi,
                    graph_edge_eta,
                    graph_block_cell,
                    graph_map,
                    graph_Index,
                

                ) = datasets.create_next_graph_fd(
                    graph_node.detach(),

     
                    graph_edge_xi.detach(),
                    graph_edge_eta.detach(),
                    graph_block_cell.detach(),
                    graph_map.detach(),
                    graph_Index.detach(),
                )
      
      
        end_time = time.time()-start_time
        print(f"End wall time:{end_time}")
        
    plot_residuals(
        f"{saving_dir}/NO.{rollout_index}_mean_u{mean_u}_rho_{rho}_mu_{mu}_dt_{dt}_{str(rollout_time_length)}steps_finetune({str(args.finetune)})_residuals.csv",
        rollout_index={rollout_index},
        re=graph_Index.pde_theta[0, -1].cpu().numpy(),
    )
    
    write_result(dataset=datasets.pool[rollout_index],
        aoa = datasets.alpha_list,
        fluid_zone_list=predicted_zone,
        result_dir=saving_dir,
        rollout_time_length=rollout_time_length,
        with_boundary=with_boundary,
        rho=rho,
        mu=mu,
        dt=dt,
        whether_has_boundary=whether_has_boundary,
        rollout_index=rollout_index,
    )


def rollout_with_fintune(
    fluid_model=None,
    datasets=None,
    rollout_index=0,
    result_dir=None,
    rollout_start_step=None,
    rollout_time_length=None,
    rho=None,
    mu=None,
    source=None,
    aoa=None,
    dt=None,
    optimizer=None,
    with_boundary=False,
    args=None,
):

    fluid_model.train()

    predicted_zone = {
        "predicted_node_uvp_list": [],
        "vis_mask":[],
        "mesh_pos":[]

    }

    (
        graph_node,

        graph_node_xi,
        graph_node_eta,
        graph_edge_xi,
        graph_edge_eta,
        graph_block_cell,
        graph_map,
        graph_Index,
        whether_has_boundary,
        origin_mesh_file_location,
    ) = loader.get_specific_data([rollout_index])
    
    graph_node.x = datasets.total_uvp_node[graph_Index.case_global_index]
    
    (
        graph_node,

        graph_node_xi,
        graph_node_eta,
        graph_edge_xi,
        graph_edge_eta,
        graph_block_cell,
        graph_map,
        graph_Index
    ) = datasets.datapreprocessing_fd(
        graph_node=graph_node.to(device),
        graph_node_xi = graph_node_xi.to(device),
        graph_node_eta = graph_node_eta.to(device),
        graph_edge_xi= graph_edge_xi.to(device),
        graph_edge_eta= graph_edge_eta.to(device),
        graph_block_cell= graph_block_cell.to(device),
        graph_map= graph_map.to(device),
        graph_Index=graph_Index.to(device),
        dimless=params.dimless,
    )

    saving_dir = f"{result_dir}/mean_u{mean_u}_mu{str(mu)}_{str(rollout_time_length)}steps_finetune({str(args.finetune)})_{rollout_index}"

    os.makedirs(saving_dir, exist_ok=True)

    # do not reset the env during inference
    datasets._set_reset_env_flag(flag=False)

    # save origin mesh file path
    with open(f"{saving_dir}/origin_mesh_path.txt", "w") as mesh_path_file:
        mesh_path_file.write(origin_mesh_file_location[0])

    # resdiual monitor
    with open(
        f"{saving_dir}/NO.{rollout_index}_mean_u{mean_u}_rho_{rho}_mu_{mu}_dt_{dt}_{str(rollout_time_length)}steps_finetune({str(args.finetune)})_residuals.csv",
        "w",
        newline="",
    ) as file:
        writer = csv.writer(file)
        writer.writerow(
            [
                "continuity_equation_residuals",
                "x_momentum_residuals",
                "y_momentum_residuals",
            ]
        )

        for epoch in range(rollout_start_step, rollout_time_length):
            last_iteration = epoch == rollout_time_length - 1

            for inner_step in range(args.inner_iteration):
                if inner_step == 0:
                    norm = True
                else:
                    norm = False
                # reset gradients
                optimizer.zero_grad()

                # forwarding the model,graph_old`s cell and edge attr has been normalized but without model upadte
                (
                predicted_node_uvp,
                loss_cont_origin,
                loss_momtentum_x,
                loss_momtentum_y,
          
                )  = fluid_model(
                    graph_node=graph_node,
    
                    graph_node_xi = graph_node_xi,
                    graph_node_eta = graph_node_eta,
                    graph_edge_xi = graph_edge_xi,
                    graph_edge_eta = graph_edge_eta,
                    graph_block_cell=graph_block_cell,
                    graph_map=graph_map,
                    graph_Index=graph_Index,
                    params=params,
                    norm=norm
                )

                batch_node_list = Batch.to_data_list(graph_node)
            
                batch_tensor = []
    
                # loss_mom = loss_momtentum_x+loss_momtentum_y
                for i,g in enumerate(batch_node_list):
            
                    mask_flow = g.mask_flow
                    count = mask_flow.sum()
                    batch_tensor.append(torch.full((count,),i))

                batch_tensor = torch.cat(batch_tensor).cuda()
            
                loss_cont = torch.sqrt(global_add_pool(loss_cont_origin**2,batch = batch_tensor).view(-1))

                loss_mom = torch.sqrt(global_add_pool(loss_momtentum_x**2,batch = batch_tensor)).view(-1)+torch.sqrt(global_add_pool(loss_momtentum_y**2,batch = batch_tensor)).view(-1) 

                loss_batch = (     
                    + params.loss_cont * loss_cont
                    + params.loss_mom * loss_mom
                )

                loss = torch.mean(torch.log(loss_batch))

                # compute gradients
                loss.backward()

                # perform optimization step
                optimizer.step()

            predicted_zone = store_predicted_uvp(
                predicted_node_uvp=predicted_node_uvp.detach(),
                predicted_zone=predicted_zone,
                graph_node=graph_node.detach(),
                end=last_iteration,
                save_last=args.save_last,
            )

            continuity_equation_residuals = abs(loss_cont.detach().mean().item())
            x_momentum_residuals = abs(loss_momtentum_x.detach().mean().item())
            y_momentum_residuals = abs(loss_momtentum_y.detach().mean().item())

            writer.writerow(
                [
                    continuity_equation_residuals,
                    x_momentum_residuals,
                    y_momentum_residuals,
                ]
            )

            if not last_iteration:
                graph_node.x = predicted_node_uvp.detach()
                (
                    graph_node,

                    graph_node_xi,
                    graph_node_eta,
                    graph_edge_xi,
                    graph_edge_eta,
                    graph_block_cell,
                    graph_map,
                    graph_Index,


                ) = datasets.create_next_graph_fd(
                    graph_node.detach(),

                    graph_node_xi.detach(),
                    graph_node_eta.detach(),
                    graph_edge_xi.detach(),
                    graph_edge_eta.detach(),
                    graph_block_cell.detach(),
                    graph_map.detach(),
                    graph_Index.detach(),
                )

    plot_residuals(
        f"{saving_dir}/NO.{rollout_index}_mean_u{mean_u}_rho_{rho}_mu_{mu}_dt_{dt}_{str(rollout_time_length)}steps_finetune({str(args.finetune)})_residuals.csv",
        rollout_index={rollout_index},
        re=graph_Index.pde_theta[0, -1].cpu().numpy(),
    )

    write_result(dataset=datasets.pool[rollout_index],
        fluid_zone_list=predicted_zone,
        result_dir=saving_dir,
        rollout_time_length=rollout_time_length,
        with_boundary=with_boundary,
        rho=rho,
        mu=mu,
        dt=dt,
        whether_has_boundary=whether_has_boundary,
        rollout_index=rollout_index,
    )


if __name__ == "__main__":
    torch.manual_seed(0)

    # configurate parameters
    def str2bool(v):
        """
        'boolean type variable' for add_argument
        """
        if v.lower() in ("yes", "true", "t", "y", "1"):
            return True
        elif v.lower() in ("no", "false", "f", "n", "0"):
            return False
        else:
            raise argparse.ArgumentTypeError("boolean value expected.")

    parser = argparse.ArgumentParser(description="Implementation of MeshGraphNets")
    parser.add_argument("--gpu", type=int, default=0, help="gpu number: 0 or 1")
    parser.add_argument(
        "--model_dir",
        type=str,
        default="/home/<USER>/Mycode/OversetGrids/Logger/net GN-Node; hs 64; training_flow_type far;/0012-2412-trained/states/130.state",
    )
    parser.add_argument(
        "--batch_size", type=int, default=1, help="test batch size at once forward"
    )
    parser.add_argument("--split", type=str, default="train")
    parser.add_argument("--rollout_num", type=int, default=1)
    parser.add_argument("--rollout_start_step", type=int, default=0)
    parser.add_argument("--rollout_time_length", type=int, default=500)
    parser.add_argument("--inner_iteration", type=int, default=20)

    parser.add_argument(
        "--spec_u_rho_mu_comb",
        nargs="*",
        type=float,
        default=[2, 5, 0.005, 0.01,1*(2*np.pi),1,-15],
        help="mean_u, rho, mu ,and dt,omiga,f,mag",
    )
    parser.add_argument(
        "--farfield_bc_type",
        type=str,
        default="uniform_velocity_field",
        choices=["uniform_velocity_field", "parabolic_velocity_field"],
    )
    parser.add_argument(
        "--rollout_index",
        type=int,
        default=1,
    )
    parser.add_argument("--save_last", type=bool, default=True)
    parser.add_argument("--finetune", type=bool, default=False)

    args = parser.parse_args()
    params = get_param.params(os.path.split(args.model_dir)[0])
    params.rollout = True

    # gpu devices
    torch.cuda.set_device(0)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")


    # initialize flow parameters
    rollout_time_length = args.rollout_time_length
    mean_u, rho, mu, dt,omi,f,mag = (
        args.spec_u_rho_mu_comb
    )
    print("fluid parameters rho:{0} mu:{1} dt:{2}".format(rho, mu, dt))

    # initialize Logger and load model / optimizer if according parameters were given
    logger = Logger(
        get_hyperparam(params), use_csv=False, use_tensorboard=False, copy_code=False
    )
    params.load_index = 0 if params.load_index is None else params.load_index

    # initialize Training Dataset
    start = time.time()
    datasets_factory = Load_mesh_old.DatasetFactory(
        params=params,
        is_training=False,
        split="train",
        dataset_dir=params.dataset_dir,
        spec_u_rho_mu_comb=args.spec_u_rho_mu_comb,
        inflow_bc_type="parabolic_velocity_field",
        device=device,
    )

    # refresh datasets size
    params.dataset_size = datasets_factory.dataset_size

    # create datasets objetc
    datasets, loader, sampler = datasets_factory.create_datasets(
        batch_size=params.batch_size, num_workers=0, pin_memory=False
    )

    end = time.time()
    print("traj has been loaded time consuming:{0}".format(end - start))

    # initialize fluid model
    model = Simulator(
    message_passing_num=params.message_passing_num,
    node_input_size=params.node_input_size + params.node_one_hot,
    edge_input_size=params.edge_input_size,
    node_output_size=params.node_output_size,
    hidden_size=params.hidden_size,
    normlizer_steps=25 * ceil(params.dataset_size / params.batch_size),
    device=device,
)

    model.load_checkpoint(device=device, is_training=False, ckpdir=args.model_dir)
    fluid_model = model.to(device)
    optimizer = Adam(fluid_model.parameters(), lr=1e-4)

    result_dir = f"{os.path.split(os.path.split(args.model_dir)[0])[0]}/rollout_with_{args.split}_dataset"
    nepochs = os.path.split(args.model_dir)[1].split(".")[0]


    result_dir = f"{result_dir}/epochs_{nepochs}"
    os.makedirs(result_dir, exist_ok=True)

    rollout_start_step = args.rollout_start_step
    roll_outs = []
    last_time = time.time()

    result_dir = f"{result_dir}/rollout_index_{args.rollout_index}"
    os.makedirs(result_dir, exist_ok=True)

    if args.finetune:

        rollout_with_fintune(
            fluid_model=fluid_model,
            datasets=datasets,
            rollout_index=args.rollout_index,
            result_dir=result_dir,
            rollout_start_step=rollout_start_step,
            rollout_time_length=rollout_time_length,
            rho=rho,
            mu=mu,
            dt=dt,
            optimizer=optimizer,
            with_boundary=True,
            args=args,
        )

    else:

        rollout_without_fintune(
            fluid_model=fluid_model,
            datasets=datasets,
            rollout_index=args.rollout_index,
            result_dir=result_dir,
            rollout_start_step=rollout_start_step,
            rollout_time_length=rollout_time_length,
            rho=rho,
            mu=mu,
            dt=dt,
            optimizer=optimizer,
            with_boundary=True,
            args=args,
        )
