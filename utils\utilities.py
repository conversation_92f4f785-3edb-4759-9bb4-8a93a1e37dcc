import numpy as np
from torch_geometric.data import Data
import enum
import torch
from torch_scatter import scatter,scatter_add
import torch.nn.functional as F


class NodeType(enum.IntEnum):
    NORMAL = 0
    INFLOW = 1
    OUTFLOW = 2
    WALL_BOUNDARY = 3
    PRESS_POINT = 4
    INTERFACE =  5
    SYMETRY =  6
    FARFIELD = 7
    NEAR_BOUNDARY = 8

def generate_boundary_zone(
    dataset=None, rho=None, mu=None, dt=None
):
 
    mesh_pos = dataset["mesh_pos_uns"].to(torch.float32)
    node_type = dataset["node_type_uns"]
    
    boundary_zone = {"name": "OBSTACLE", "rho": rho, "mu": mu, "dt": dt}
    boundary_zone["zonename"] = "OBSTICALE_BOUNDARY"
    node_topwall = torch.max(mesh_pos[:,1])
    node_bottomwall = torch.min(mesh_pos[:,1])
    node_outlet = torch.max(mesh_pos[:,0])
    node_inlet = torch.min(mesh_pos[:,0])

    MasknodeT = torch.full((mesh_pos.shape[0],1),True).squeeze(1) 
    MasknodeF = torch.logical_not(MasknodeT) 


    
    mask_node_boundary = torch.where(((((node_type==NodeType.WALL_BOUNDARY)))&(mesh_pos[:,1:2]<node_topwall).squeeze(1) &(mesh_pos[:,1:2]>node_bottomwall).squeeze(1) &(mesh_pos[:,0:1]>node_inlet).squeeze(1) &(mesh_pos[:,0:1]<node_outlet).squeeze(1) ),MasknodeT,MasknodeF)   

    bc_pos = mesh_pos[mask_node_boundary]

    num_bc_nodes = mask_node_boundary.sum()

    col = torch.arange(num_bc_nodes, dtype=torch.long)
    row = col.clone()+1
    row[-1] = 0

    boundary_zone["face_node"] = torch.stack((row,col),dim=-1)

    boundary_zone["mesh_pos"] = bc_pos.unsqueeze(0)

    boundary_zone["mask_node_boundary"] = mask_node_boundary

    return boundary_zone

def moments_order(
    order="1nd",
    mesh_pos_diff_on_edge=None,
    indegree_node_index=None,
):
    '''
    mesh_pos_diff_on_edge:[2*E, 2]
    indegree_node_index:[N]
    '''
    
    if order=="1st":
        od=2
        displacement = mesh_pos_diff_on_edge.unsqueeze(2)
        
    elif order=="2nd":
        od=3
        displacement = torch.cat(
            (
                mesh_pos_diff_on_edge,
                0.5 * (mesh_pos_diff_on_edge**2),
                mesh_pos_diff_on_edge[:, 0:1] * mesh_pos_diff_on_edge[:, 1:2],
            ),
            dim=-1,
        ).unsqueeze(2)
        
    elif order=="3rd":
        od=4
        displacement = torch.cat(
            (
                mesh_pos_diff_on_edge,
                0.5 * (mesh_pos_diff_on_edge**2),
                mesh_pos_diff_on_edge[:, 0:1] * mesh_pos_diff_on_edge[:, 1:2],
                (1 / 6) * (mesh_pos_diff_on_edge**3),
                0.5 * (mesh_pos_diff_on_edge[:, 0:1] ** 2) * mesh_pos_diff_on_edge[:, 1:2],
                0.5 * (mesh_pos_diff_on_edge[:, 1:2] ** 2) * mesh_pos_diff_on_edge[:, 0:1],
            ),
            dim=-1,
        ).unsqueeze(2)
        
    elif order=="4th":
        od=5
        displacement = torch.cat(
            (
                mesh_pos_diff_on_edge,
                0.5 * (mesh_pos_diff_on_edge**2),
                mesh_pos_diff_on_edge[:, 0:1] * mesh_pos_diff_on_edge[:, 1:2],
                (1 / 6) * (mesh_pos_diff_on_edge**3),
                0.5 * (mesh_pos_diff_on_edge[:, 0:1] ** 2) * mesh_pos_diff_on_edge[:, 1:2],
                0.5 * (mesh_pos_diff_on_edge[:, 1:2] ** 2) * mesh_pos_diff_on_edge[:, 0:1],
                (1 / 24) * (mesh_pos_diff_on_edge[:, 0:1] ** 4),
                (1 / 6)
                * (mesh_pos_diff_on_edge[:, 0:1] ** 3)
                * mesh_pos_diff_on_edge[:, 1:2],
                (1 / 4)
                * (mesh_pos_diff_on_edge[:, 0:1] ** 2)
                * (mesh_pos_diff_on_edge[:, 1:2] ** 2),
                (1 / 6)
                * (mesh_pos_diff_on_edge[:, 0:1])
                * (mesh_pos_diff_on_edge[:, 1:2] ** 3),
                (1 / 24) * (mesh_pos_diff_on_edge[:, 1:2] ** 4),
            ),
            dim=-1,
        ).unsqueeze(2)
    else:
        raise NotImplementedError(f"{order} Order not implemented")
    
    displacement_T = displacement.transpose(1, 2)

    weight_node_to_node = (1 / torch.norm(mesh_pos_diff_on_edge, dim=1, keepdim=True)**od).unsqueeze(2)
        
    left_on_edge = torch.matmul(
        displacement * weight_node_to_node,
        displacement_T,
    )

    A_node_to_node = scatter_add(
        left_on_edge, indegree_node_index, dim=0
    ) # [N, x, x], x is depend on order
    
    B_node_to_node = weight_node_to_node * displacement
    # [2*E, x]
    
    return A_node_to_node, B_node_to_node

def compute_normal_matrix(
    order="1st",
    mesh_pos=None,
    outdegree=None,
    indegree=None,
    dual_edge=True, # 输入的in/outdegree是否是双向的
):
    """
    Computes the normal matrices A and B for node-based weighted least squares (WLSQ)
    gradient reconstruction.

    Parameters:
    - order (str): The order of the reconstruction ('1st', '2nd', '3rd', or '4th').
    - mesh_pos (torch.Tensor): Tensor of shape [N, D] containing the positions of the mesh nodes.
    - outdegree (torch.Tensor): Tensor containing the indices of source nodes (outgoing edges).
    - indegree (torch.Tensor): Tensor containing the indices of target nodes (incoming edges).
    - dual_edge (bool): If True, the provided outdegree and indegree represent bidirectional edges.
                        If False, the function constructs bidirectional edges by concatenating
                        the input edges.

    Returns:
    - A_node_to_node (torch.Tensor): Normal matrix A for each node.
    - B_node_to_node (torch.Tensor): Matrix B for each node.
    """
    
    if dual_edge:
        outdegree_node_index, indegree_node_index = outdegree, indegree
    else:
        outdegree_node_index = torch.cat((outdegree, indegree), dim=0)
        indegree_node_index = torch.cat((indegree, outdegree), dim=0)
        
    mesh_pos_diff_on_edge = mesh_pos[outdegree_node_index] - mesh_pos[indegree_node_index]

    (A_node_to_node, B_node_to_node) = moments_order(
        order=order,
        mesh_pos_diff_on_edge=mesh_pos_diff_on_edge,
        indegree_node_index=indegree_node_index,
    )

    return (A_node_to_node, B_node_to_node)

def node_based_WLSQ(
    phi_node=None,
    edge_index=None,
    mesh_pos=None,
    dual_edge=True, # 输入的edge_index是否是双向的
    order=None,
    precompute_Moments: list = None,

):
    '''
    B right-hand sides in precompute_Moments must be SINGLE-WAY
    on edge
    '''
    # edge_index = knn_graph(mesh_pos, k=9, loop=False)
    if (order is None) or (order not in ["1st", "2nd", "3rd", "4th"]):
        raise ValueError("order must be specified in [\"1st\", \"2nd\", \"3rd\", \"4th\"]")
    
    if dual_edge:
        outdegree_node_index, indegree_node_index = edge_index[0], edge_index[1]
    else:
        outdegree_node_index = torch.cat((edge_index[0], edge_index[1]), dim=0)
        indegree_node_index = torch.cat((edge_index[1], edge_index[0]), dim=0)

    if precompute_Moments is None:

        """node to node contribution"""
        (A_node_to_node, two_way_B_node_to_node) = compute_normal_matrix(
            order=order,
            mesh_pos=mesh_pos,
            outdegree=outdegree_node_index,
            indegree=indegree_node_index,
            dual_edge=False if dual_edge else True,
        )
        """node to node contribution"""

        phi_diff_on_edge = two_way_B_node_to_node * (
            (phi_node[outdegree_node_index] - phi_node[indegree_node_index]).unsqueeze(
                1
            )
        )

        B_phi_node_to_node = scatter_add(
            phi_diff_on_edge, indegree_node_index, dim=0, dim_size=mesh_pos.shape[0]
        )

    else:
        """use precomputed moments"""
        A_node_to_node, Oneway_B_node_to_node = precompute_Moments

        half_dim = Oneway_B_node_to_node.shape[0]
        
        two_way_B_node_to_node = torch.cat(
            (Oneway_B_node_to_node, Oneway_B_node_to_node), dim=0
        )
        
        # 大于1阶的奇数阶项需要取负
        two_way_B_node_to_node[half_dim:,0:2]*= -1
        od = int(order[0])
        
        if od >=3 :
            two_way_B_node_to_node[half_dim:,5:9]*= -1
            
        phi_diff_on_edge = two_way_B_node_to_node * (
            (phi_node[outdegree_node_index] - phi_node[indegree_node_index]).unsqueeze(
                1
            )
        )

        B_phi_node_to_node = scatter_add(
            phi_diff_on_edge,
            indegree_node_index,
            dim=0,
            dim_size=mesh_pos.shape[0],
        )
        
    # 行归一化
    row_norms = torch.norm(A_node_to_node, p=2, dim=2, keepdim=True)
    A_normalized = A_node_to_node / (row_norms + 1e-8)
    B_normalized = B_phi_node_to_node / (row_norms + 1e-8)
    
    # lambda_reg = 1e-5  # 正则化参数
    # I = torch.eye(A_normalized.shape[-1], device=A_normalized.device)
    # A_normalized = A_normalized + lambda_reg * I
    
    # # 列归一化
    # col_norms = torch.norm(A_normalized, p=2, dim=1, keepdim=True)
    # A_normalized = A_normalized / (col_norms + 1e-8)
    # B_normalized = B_normalized * col_norms
    
    """ first method"""
    # nabla_phi_node_lst = torch.linalg.lstsq(
    #     A_normalized, B_normalized
    # ).solution.transpose(1, 2)

    """ second method"""
    # nabla_phi_node_lst = torch.matmul(A_inv_node_to_node_x,B_phi_node_to_node_x)

    """ third method"""
    nabla_phi_node_lst = torch.linalg.solve(
        A_normalized, B_normalized
    ).transpose(1, 2)

    """ fourth method"""
    # nabla_phi_node_lst = torch.matmul(R_inv_Q_t,B_phi_node_to_node_x)

    return nabla_phi_node_lst

def calc_cell_centered_with_node_attr(
    node_attr, cells_node, cells_index, reduce="mean", map=True
):
    if cells_node.shape != cells_index.shape:
        raise ValueError("wrong cells_node/cells_index dim")

    if len(cells_node.shape) > 1:
        cells_node = cells_node.view(-1)

    if len(cells_index.shape) > 1:
        cells_index = cells_index.view(-1)

    if map:
        mapped_node_attr = node_attr[cells_node]
    else:
        mapped_node_attr = node_attr

    cell_attr = scatter(src=mapped_node_attr, index=cells_index, dim=0, reduce=reduce)

    return cell_attr


def calc_node_centered_with_cell_attr(
    cell_attr, cells_node, cells_index, reduce="mean", map=True
):
    if cells_node.shape != cells_index.shape:
        raise ValueError(f"wrong cells_node/cells_index dim ")

    if len(cells_node.shape) > 1:
        cells_node = cells_node.view(-1)

    if len(cells_index.shape) > 1:
        cells_index = cells_index.view(-1)

    if map:
        maped_cell_attr = cell_attr[cells_index]
    else:
        maped_cell_attr = cell_attr

    cell_attr = scatter(src=maped_cell_attr, index=cells_node, dim=0, reduce=reduce)

    return cell_attr


# see https://github.com/sungyongs/dpgn/blob/master/utils.py
def decompose_and_trans_node_attr_to_cell_attr_graph(
    graph, has_changed_node_attr_to_cell_attr
):
    # graph: torch_geometric.data.data.Data
    # TODO: make it more robust
    x, edge_index, edge_attr, face, global_attr, mask_cell_interior = (
        None,
        None,
        None,
        None,
        None,
        None,
    )

    for key in graph.keys():
        if key == "x":
            x = graph.x  # avoid exception
        elif key == "edge_index":
            edge_index = graph.edge_index
        elif key == "edge_attr":
            edge_attr = graph.edge_attr
        elif key == "global_attr":
            global_attr = graph.global_attr
        elif key == "face":
            face = graph.face
        elif key == "mask_cell_interior":
            mask_cell_interior = graph.mask_cell_interior
        else:
            pass

    return (x, edge_index, edge_attr, face, global_attr, mask_cell_interior)


# see https://github.com/sungyongs/dpgn/blob/master/utils.py
def copy_geometric_data(graph, has_changed_node_attr_to_cell_attr):
    """return a copy of torch_geometric.data.data.Data
    This function should be carefully used based on
    which keys in a given graph.
    """
    (
        node_attr,
        edge_index,
        edge_attr,
        face,
        global_attr,
        mask_cell_interior,
    ) = decompose_and_trans_node_attr_to_cell_attr_graph(
        graph, has_changed_node_attr_to_cell_attr
    )

    ret = Data(
        x=node_attr,
        edge_index=edge_index,
        edge_attr=edge_attr,
        face=face,
        mask_cell_interior=mask_cell_interior,
    )

    ret.global_attr = global_attr

    return ret


def shuffle_np(array):
    array_t = array.copy()
    np.random.shuffle(array_t)
    return array_t





def extract_cylinder_boundary(
    dataset,
    aoa,
    dataset_all,
    graph_node: Data,

    rho=None,
    mu=None,
    dt=None,
):
    write_zone = {"zonename": "OBSTACLE", "rho": rho, "mu": mu, "dt": dt}
    node_type = graph_node.node_type
    mesh_pos = graph_node.pos
    node_topwall = torch.max(mesh_pos[:,1])
    node_bottomwall = torch.min(mesh_pos[:,1])
    node_outlet = torch.max(mesh_pos[:,0])
    node_inlet = torch.min(mesh_pos[:,0])

    MasknodeT = torch.full((mesh_pos.shape[0],1),True).cuda()
    MasknodeF = torch.logical_not(MasknodeT).cuda()
    mask_node_boundary = torch.where(((((node_type==NodeType.WALL_BOUNDARY)|(node_type==NodeType.INTERFACE_WALL)))&(mesh_pos[:,1:2]<node_topwall)&(mesh_pos[:,1:2]>node_bottomwall)&(mesh_pos[:,0:1]>node_inlet)&(mesh_pos[:,0:1]<node_outlet)),MasknodeT,MasknodeF).squeeze(1)
    
    write_zone["mesh_pos"] = (
        graph_node.pos[mask_node_boundary, 0:2].to("cpu").unsqueeze(0).numpy()
    )

    write_zone["velocity"] = (
        dataset["predicted_node_uvp"][:, mask_node_boundary, 0:2].to("cpu").numpy()
    )

    write_zone["pressure"] = (
        dataset["predicted_node_uvp"][:, mask_node_boundary, 2:3].to("cpu").numpy()
    )


 



    write_zone["mask_node_boundary"] = mask_node_boundary



    num_bc_nodes = mask_node_boundary.sum()

    col = torch.arange(num_bc_nodes, dtype=torch.long)
    row = col.clone()+1
    row[-1] = 0
    for t in range(write_zone["velocity"].shape[0]):
        alpha =  aoa[t]   
    
        wall_p = dataset["predicted_node_uvp"][t,mask_node_boundary,2:3]

        boundary_mesh_pos = mesh_pos[mask_node_boundary,:]


        grad_uv = node_based_WLSQ(
            phi_node=dataset["predicted_node_uvp"][t,:,0:2].cuda(),
            edge_index=dataset_all["support_edge"].cuda(),
            mesh_pos=dataset_all["mesh_pos"][0].cuda().float(),
            dual_edge=False,
            order="2nd",
            precompute_Moments=None,
        )
        grad_wall = grad_uv[mask_node_boundary,:,0:2]
        
        grad_wall_face = (grad_wall[row]+grad_wall[col])/2

        wall_face_length = (boundary_mesh_pos[row]- boundary_mesh_pos[col]).norm(dim=1,keepdim=True)

        wall_tan = -(boundary_mesh_pos[row]- boundary_mesh_pos[col])/wall_face_length

        wall_nor = torch.cat([-wall_tan[:,1:2], wall_tan[:,0:1]], dim=1)

        u_xy = grad_wall_face[:,0,:]
        v_xy = grad_wall_face[:,1,:]

        u_r = (u_xy * wall_nor).sum(1, keepdim=True)

        v_r = (v_xy * wall_nor).sum(1, keepdim=True)

        uth_r = u_r * wall_tan[:,0:1] + v_r *wall_tan[:,1:2]  

        wall_face_p = (wall_p[row]+wall_p[col])/2

        Cp = wall_face_p/(0.5*rho*dataset_all["inf_u"]**2) 

        Cf = -uth_r*mu/(0.5*rho*dataset_all["inf_u"]**2)

        Force_p = Cp*wall_face_length*wall_nor

        Force_f = Cf*wall_face_length*wall_tan

      

        Cl_p = (torch.cos(alpha)*Force_p[:,1:2] - torch.sin(alpha)*Force_p[:,0:1]).sum()
        Cd_p = (torch.sin(alpha)*Force_p[:,1:2] + torch.cos(alpha)*Force_p[:,0:1]).sum()

        Cl_f = (torch.cos(alpha)*Force_f[:,1:2] - torch.sin(alpha)*Force_f[:,0:1]).sum()
        Cd_f = (torch.sin(alpha)*Force_f[:,1:2] + torch.cos(alpha)*Force_f[:,0:1]).sum()

        L = 0.05

        Cl = (Cl_p + Cl_f)/L
        Cd = (Cd_p + Cd_f)/L


        dataset["CL_list"].append(Cl.to("cpu").numpy()) 
        dataset["CD_list"].append(Cd.to("cpu").numpy())    
    

    write_zone["face"] = torch.stack((row,col),dim=-1).unsqueeze(0).cpu().numpy()
    write_zone["mesh_pos"]  = dataset["mesh_pos"][:, mask_node_boundary, 0:2].to("cpu").numpy()
    
    write_zone["zonename"] = "OBSTICALE_BOUNDARY"

    overset_nodetype = np.full((write_zone["mesh_pos"].shape[1], 1), 1)
    write_zone["overset_nodetype"]  = overset_nodetype
    write_zone["data_packing_type"]  = "node"
    return write_zone



def extract_cylinder_boundary_only_training(
    dataset=None, rho=None, mu=None, dt=None
):
    node_type = dataset["node_type"][0]
    face_node = dataset["face"][0].long()
    if face_node.shape[0]>face_node.shape[1]:
        face_node = face_node.T
    mesh_pos = dataset["mesh_pos"][0]
    boundary_zone = {"name":"OBSTACLE",
                    "rho":rho,
                    "mu":mu,
                    "dt":dt} 



    node_topwall = torch.max(mesh_pos[:,1])
    node_bottomwall = torch.min(mesh_pos[:,1])
    node_outlet = torch.max(mesh_pos[:,0])
    node_inlet = torch.min(mesh_pos[:,0])

    MasknodeT = torch.full((mesh_pos.shape[0],1),True)
    MasknodeF = torch.logical_not(MasknodeT)


    
    mask_node_boundary = torch.where(((((node_type==NodeType.WALL_BOUNDARY)))&(mesh_pos[:,1:2]<node_topwall)&(mesh_pos[:,1:2]>node_bottomwall)&(mesh_pos[:,0:1]>node_inlet)&(mesh_pos[:,0:1]<node_outlet)),MasknodeT,MasknodeF).squeeze(1)
    




    boundary_zone["mask_node_boundary"] = mask_node_boundary


    num_bc_nodes = mask_node_boundary.sum()



    


    col = torch.arange(num_bc_nodes, dtype=torch.long)
    row = col.clone()+1
    row[-1] = 0
    
    boundary_mesh_pos = mesh_pos[mask_node_boundary,:]

    wall_p = dataset["pressure_on_node"][0,mask_node_boundary,:]


    grad_uv = node_based_WLSQ(
        phi_node=dataset["velocity_on_node"][0],
        edge_index=dataset["support_edge"],
        mesh_pos=dataset["mesh_pos"][0],
        dual_edge=False,
        order="2nd",
        precompute_Moments=None,
    )
    grad_wall = grad_uv[mask_node_boundary,:,0:2]
    
    grad_wall_face = (grad_wall[row]+grad_wall[col])/2

    wall_face_length = (boundary_mesh_pos[row]- boundary_mesh_pos[col]).norm(dim=1,keepdim=True)

    wall_tan = -(boundary_mesh_pos[row]- boundary_mesh_pos[col])/wall_face_length

    wall_nor = torch.cat([-wall_tan[:,1:2], wall_tan[:,0:1]], dim=1)

    u_xy = grad_wall_face[:,0,:]
    v_xy = grad_wall_face[:,1,:]

    u_r = (u_xy * wall_nor).sum(1, keepdim=True)

    v_r = (v_xy * wall_nor).sum(1, keepdim=True)

    uth_r = u_r * wall_tan[:,0:1] + v_r *wall_tan[:,1:2]  

    wall_face_p = (wall_p[row]+wall_p[col])/2

    Cp = wall_face_p/(0.5*rho*dataset["inf_u"]**2) 

    Cf = -uth_r*mu/(0.5*rho*dataset["inf_u"]**2)

    Force_p = Cp*wall_face_length*wall_nor

    Force_f = Cf*wall_face_length*wall_tan

    alpha = dataset["alpha"]

    Cl_p = (torch.cos(alpha)*Force_p[:,1:2] - torch.sin(alpha)*Force_p[:,0:1]).sum()
    Cd_p = (torch.sin(alpha)*Force_p[:,1:2] + torch.cos(alpha)*Force_p[:,0:1]).sum()

    Cl_f = (torch.cos(alpha)*Force_f[:,1:2] - torch.sin(alpha)*Force_f[:,0:1]).sum()
    Cd_f = (torch.sin(alpha)*Force_f[:,1:2] + torch.cos(alpha)*Force_f[:,0:1]).sum()

    L = 0.05

    Cl = (Cl_p + Cl_f)/L
    Cd = (Cd_p + Cd_f)/L


    dataset["Cl"] = Cl
    dataset["Cd"] = Cd

    boundary_zone["face"] = torch.stack((row,col),dim=-1).unsqueeze(0).numpy()
    boundary_zone["mesh_pos"] = boundary_mesh_pos.unsqueeze(0).numpy()
    boundary_zone["zonename"] = "OBSTICALE_BOUNDARY"

    overset_nodetype = np.full((boundary_zone["mesh_pos"].shape[1], 1), 1)

        


        

    return boundary_zone




def is_point_in_polygon_vectorized(points, polygon):
    polygon = np.vstack([polygon, polygon[0]])  # 闭合多边形
    edges = polygon[1:] - polygon[:-1]         # 多边形的边
    test_vectors = points[:, None, :] - polygon[:-1]  # 点到多边形顶点的向量
    
    cross_products = np.cross(edges, test_vectors)    # 计算向量叉积
    signs = np.sign(cross_products)                   # 获取叉积符号
    is_inside = np.all(signs == signs[:, [0]], axis=1)  # 判断符号是否一致
    return is_inside

id1_kenerl = torch.Tensor([[1, 0],[0, 0]]).unsqueeze(0).unsqueeze(0)
def get_id1(id):
    return F.conv2d(id, id1_kenerl, padding=(0, 0))

id2_kenerl = torch.Tensor([[0, 1],[0, 0]]).unsqueeze(0).unsqueeze(0)
def get_id2(id):
    return F.conv2d(id, id2_kenerl, padding=(0, 0))

id3_kenerl = torch.Tensor([[0, 0],[0, 1]]).unsqueeze(0).unsqueeze(0)
def get_id3(id):
    return F.conv2d(id, id3_kenerl, padding=(0, 0))

id4_kenerl = torch.Tensor([[0, 0],[1, 0]]).unsqueeze(0).unsqueeze(0)
def get_id4(id):
    return F.conv2d(id, id4_kenerl, padding=(0, 0))

n_kernel =torch.Tensor([[0, 1,0],[0, 0,0],[0, 0,0]]).unsqueeze(0).unsqueeze(0)
def find_north(v):
    return F.conv2d(v, n_kernel, padding=(0, 0))

s_kernel = torch.Tensor([[0, 0,0],[0, 0,0],[0, 1,0]]).unsqueeze(0).unsqueeze(0)
def find_south(v):
    return F.conv2d(v, s_kernel, padding=(0, 0))

w_kernel = torch.Tensor([[0, 0,0],[1, 0,0],[0, 0,0]]).unsqueeze(0).unsqueeze(0)
def find_west(v):
    return F.conv2d(v, w_kernel, padding=(0, 0))

e_kernel = torch.Tensor([[0, 0,0],[0, 0,1],[0, 0,0]]).unsqueeze(0).unsqueeze(0)

def find_east(v):
    return F.conv2d(v, e_kernel, padding=(0, 0))

mid_kernel = torch.Tensor([[0, 0,0],[0, 1,0],[0, 0,0]]).unsqueeze(0).unsqueeze(0)
def find_mid(v):
    return F.conv2d(v, mid_kernel, padding=(0, 0))



left_kernel = torch.Tensor([1, 0]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def get_left(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), left_kernel, padding=(0, 0)).long().reshape(-1).numpy()

right_kernel = torch.Tensor([0, 1]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def get_right(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), right_kernel, padding=(0, 0)).long().reshape(-1).numpy()

up_kernel = torch.Tensor([1, 0]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def get_up(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), up_kernel, padding=(0, 0)).long().reshape(-1).numpy()

down_kernel = torch.Tensor([0, 1]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def get_down(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), down_kernel, padding=(0, 0)).long().reshape(-1).numpy()


left_kernel_node = torch.Tensor([1, 0,0]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def get_left_node(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), left_kernel_node, padding=(0, 0)).long().reshape(-1).numpy()

right_kernel_node = torch.Tensor([0,0, 1]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def get_right_node(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), right_kernel_node, padding=(0, 0)).long().reshape(-1).numpy()

up_kernel_node = torch.Tensor([1, 0,0]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def get_up_node(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), up_kernel_node, padding=(0, 0)).long().reshape(-1).numpy()

down_kernel_node= torch.Tensor([0,0, 1]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def get_down_node(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), down_kernel_node, padding=(0, 0)).long().reshape(-1).numpy()

kernel_1 = torch.Tensor([[1, 0,0],[0, 0,0],[0, 0,0]]).unsqueeze(0).unsqueeze(0)
def node_1(v):
    return F.conv2d(v, kernel_1, padding=(0, 0))

kernel_3 = torch.Tensor([[0, 0,1],[0, 0,0],[0, 0,0]]).unsqueeze(0).unsqueeze(0)
def node_3(v):
    return F.conv2d(v, kernel_3, padding=(0, 0))

kernel_7 = torch.Tensor([[0, 0,0],[0, 0,0],[1, 0,0]]).unsqueeze(0).unsqueeze(0)
def node_7(v):
    return F.conv2d(v, kernel_7, padding=(0, 0))

kernel_9 = torch.Tensor([[0, 0,0],[0, 0,0],[0, 0,1]]).unsqueeze(0).unsqueeze(0)
def node_9(v):
    return F.conv2d(v, kernel_9, padding=(0, 0))

kenerl_ww = torch.Tensor([1, 0,0,0,0]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def ww_node(v):
    return F.conv2d(v, kenerl_ww, padding=(0, 0))

kenerl_w = torch.Tensor([0, 1,0,0,0]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def w_node(v):
    return F.conv2d(v, kenerl_w, padding=(0, 0))

kenerl_ee = torch.Tensor([0, 0,0,0,1]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def ee_node(v):
    return F.conv2d(v, kenerl_ee, padding=(0, 0))

kenerl_e = torch.Tensor([0, 0,0,1,0]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def e_node(v):
    return F.conv2d(v, kenerl_e, padding=(0, 0))

kenerl_nn = torch.Tensor([1, 0,0,0,0]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def nn_node(v):
    return F.conv2d(v, kenerl_nn, padding=(0, 0))

kenerl_n = torch.Tensor([0, 1,0,0,0]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def n_node(v):
    return F.conv2d(v, kenerl_n, padding=(0, 0))

kenerl_ss = torch.Tensor([0, 0,0,0,1]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def ss_node(v):
    return F.conv2d(v, kenerl_ss, padding=(0, 0))

kenerl_s = torch.Tensor([0, 0,0,1,0]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def s_node(v):
    return F.conv2d(v, kenerl_s, padding=(0, 0))


find_kenerl_ww = torch.Tensor([[0, 0,0,0,0],[0, 0,0,0,0],[1, 0,0,0,0],[0, 0,0,0,0],[0, 0,0,0,0]]).unsqueeze(0).unsqueeze(0)
def find_ww_node(v):
    return F.conv2d(v, find_kenerl_ww, padding=(0, 0))

find_kenerl_ee = torch.Tensor([[0, 0,0,0,0],[0, 0,0,0,0],[0, 0,0,0,1],[0, 0,0,0,0],[0, 0,0,0,0]]).unsqueeze(0).unsqueeze(0)
def find_ee_node(v):
    return F.conv2d(v, find_kenerl_ee, padding=(0, 0))

find_kenerl_nn = torch.Tensor([[0, 0,1,0,0],[0, 0,0,0,0],[0, 0,0,0,0],[0, 0,0,0,0],[0, 0,0,0,0]]).unsqueeze(0).unsqueeze(0)
def find_nn_node(v):
    return F.conv2d(v, find_kenerl_nn, padding=(0, 0))

find_kenerl_ss = torch.Tensor([[0, 0,0,0,0],[0, 0,0,0,0],[0, 0,0,0,0],[0, 0,0,0,0],[0, 0,1,0,0]]).unsqueeze(0).unsqueeze(0)
def find_ss_node(v):
    return F.conv2d(v, find_kenerl_ss, padding=(0, 0))


find_kenerl_mid_quick = torch.Tensor([[0, 0,0,0,0],[0, 0,0,0,0],[0, 0,1,0,0],[0, 0,0,0,0],[0, 0,0,0,0]]).unsqueeze(0).unsqueeze(0)
def find_mid_quick(v):
    return F.conv2d(v, find_kenerl_mid_quick, padding=(0, 0))



kenerl_muscl_2nd_ww = torch.Tensor([1, 0,0,0]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def muscl_2nd_ww(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), kenerl_muscl_2nd_ww, padding=(0, 0)).long().reshape(-1).numpy()

kenerl_muscl_2nd_w = torch.Tensor([0, 1,0,0]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def muscl_2nd_w(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), kenerl_muscl_2nd_w, padding=(0, 0)).long().reshape(-1).numpy()

kenerl_muscl_2nd_ee = torch.Tensor([ 0,0,0,1]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def muscl_2nd_ee(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), kenerl_muscl_2nd_ee, padding=(0, 0)).long().reshape(-1).numpy()

kenerl_muscl_2nd_e = torch.Tensor([0,0,1,0]).unsqueeze(0).unsqueeze(1).unsqueeze(2)
def muscl_2nd_e(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), kenerl_muscl_2nd_e, padding=(0, 0)).long().reshape(-1).numpy()

kenerl_muscl_2nd_nn = torch.Tensor([1, 0,0,0]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def muscl_2nd_nn(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), kenerl_muscl_2nd_nn, padding=(0, 0)).long().reshape(-1).numpy()

kenerl_muscl_2nd_n = torch.Tensor([0, 1,0,0]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def muscl_2nd_n(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), kenerl_muscl_2nd_n, padding=(0, 0)).long().reshape(-1).numpy()

kenerl_muscl_2nd_ss = torch.Tensor([ 0,0,0,1]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def muscl_2nd_ss(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), kenerl_muscl_2nd_ss, padding=(0, 0)).long().reshape(-1).numpy()

kenerl_muscl_2nd_s = torch.Tensor([0,0,1,0]).unsqueeze(0).unsqueeze(1).unsqueeze(3)
def muscl_2nd_s(v):
    return F.conv2d(torch.from_numpy(v).unsqueeze(0).unsqueeze(0).float(), kenerl_muscl_2nd_s, padding=(0, 0)).long().reshape(-1).numpy()

def compute_face_centers(grid_points):
    """
    快速计算结构化网格的面中点坐标
    
    Args:
        grid_points: torch.Tensor, shape (N, W, 2) 网格点坐标
                    N: i方向点数, W: j方向点数, 2: (x,y)坐标
    
    Returns:
        i_face_centers: torch.Tensor, shape (N-1, W, 2) i方向面中点
        j_face_centers: torch.Tensor, shape (N, W-1, 2) j方向面中点
    """
    
    # i方向面中点 (连接相邻i索引的点)
    # 每个i方向面连接 (i,j) 和 (i+1,j) 两个点
    i_face_centers = 0.5 * (grid_points[:-1, :, :] + grid_points[1:, :, :])
    
    # j方向面中点 (连接相邻j索引的点) 
    # 每个j方向面连接 (i,j) 和 (i,j+1) 两个点
    j_face_centers = 0.5 * (grid_points[:, :-1, :] + grid_points[:, 1:, :])
    
    return i_face_centers, j_face_centers

def gen_neighbor_cell(cell_index_unpad):
    """
    获取xi和eta方向face的相邻单元编号，并处理边界情况
    
    Args:
        cell_index_unpad: numpy.ndarray, shape (rows, cols) 单元编号矩阵
                         从上到下：i增加(xi方向)，从左到右：j增加(eta方向)
    
    Returns:
        xi_face_neighbors: tuple (up_cells, down_cells)
            - up_cells: xi方向face的上单元编号 [rows+1, cols]  
            - down_cells: xi方向face的下单元编号 [rows+1, cols]
        eta_face_neighbors: tuple (left_cells, right_cells)  
            - left_cells: eta方向face的左单元编号 [rows, cols+1]
            - right_cells: eta方向face的右单元编号 [rows, cols+1]
    """
    rows, cols = cell_index_unpad.shape
    
    # === Xi方向face的相邻单元 ===
    # Xi方向face位于相邻行之间，总共有 (rows-1) 个内部face + 2个边界face = (rows+1)个face
    
    # 内部face的邻居关系
    internal_xi_up = cell_index_unpad[:-1, :]    # 上单元：前 rows-1 行
    internal_xi_down = cell_index_unpad[1:, :]   # 下单元：后 rows-1 行
    
    # 边界face处理：使用内部face的邻居关系
    # 第一行face (边界): 复制第二行face的邻居关系 (第二行face: 上=第一行，下=第二行)
    first_face_up = cell_index_unpad[0:1, :]    # 第一行单元 (与第二行face的上单元相同)
    first_face_down = cell_index_unpad[1:2, :]  # 第二行单元 (与第二行face的下单元相同)
    
    # 最后一行face (边界): 复制倒数第二行face的邻居关系，但上下对换
    last_face_up = cell_index_unpad[-1:, :]     # 最后一行单元作为上单元 (对换)
    last_face_down = cell_index_unpad[-2:-1, :] # 倒数第二行单元作为下单元 (对换)
    
    # 组合所有xi方向face
    xi_up_cells = np.concatenate([first_face_up, internal_xi_up, last_face_up], axis=0)
    xi_down_cells = np.concatenate([first_face_down, internal_xi_down, last_face_down], axis=0)
    
    # === Eta方向face的相邻单元 ===  
    # Eta方向face位于相邻列之间，总共有 (cols-1) 个内部face + 2个边界face = (cols+1)个face
    
    # 内部face的邻居关系
    internal_eta_left = cell_index_unpad[:, :-1]   # 左单元：前 cols-1 列
    internal_eta_right = cell_index_unpad[:, 1:]   # 右单元：后 cols-1 列
    
    # 边界face处理：
    # 第一列face (边界): 左单元=第一列单元，右单元=第二列单元
    first_col_left = cell_index_unpad[:, 0:1]      # 第一列单元作为左单元
    first_col_right = cell_index_unpad[:, 1:2]     # 第二列单元作为右单元
    
    # 最后一列face (边界): 复制倒数第二列face的邻居关系，但左右对换
    last_col_left = cell_index_unpad[:, -1:]       # 最后一列单元作为左单元 (对换)
    last_col_right = cell_index_unpad[:, -2:-1]    # 倒数第二列单元作为右单元 (对换)
    
    # 组合所有eta方向face
    eta_left_cells = np.concatenate([first_col_left, internal_eta_left, last_col_left], axis=1)
    eta_right_cells = np.concatenate([first_col_right, internal_eta_right, last_col_right], axis=1)
    
    return (xi_up_cells, xi_down_cells), (eta_left_cells, eta_right_cells)

def gen_neighbor_cell_flattened(cell_index_unpad):
    """
    获取face相邻单元编号并展平为一维数组
    
    Args:
        cell_index_unpad: numpy.ndarray, shape (rows, cols)
    
    Returns:
        xi_neighbors_flat: tuple (up_cells_flat, down_cells_flat)
        eta_neighbors_flat: tuple (left_cells_flat, right_cells_flat)
    """
    (xi_up, xi_down), (eta_left, eta_right) = gen_neighbor_cell(cell_index_unpad)
    
    return np.concatenate((np.stack((xi_up.flatten(), xi_down.flatten()),axis=0),np.stack((eta_left.flatten(), eta_right.flatten()),axis=0)),axis=-1)




