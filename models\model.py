import torch.nn as nn
import torch
from .blocks import <PERSON><PERSON><PERSON>, NodeBlock
from utils.utilities import (
    decompose_and_trans_node_attr_to_cell_attr_graph,
    copy_geometric_data,
    NodeType,
)
from torch_geometric.data import Data
from torch_geometric.data.batch import Batch
from torch_geometric.nn import global_add_pool,global_mean_pool
from utils.normalization import normalize_grads


def build_mlp(
    in_size, hidden_size, out_size, drop_out=True, lay_norm=True, dropout_prob=0.2
):
    if drop_out:
        module = nn.Sequential(
            nn.Linear(in_size, hidden_size),
            nn.Dropout(p=dropout_prob),
            nn.GELU(),
            nn.Linear(hidden_size, hidden_size),
            nn.Dropout(p=dropout_prob),
            nn.GELU(),
            nn.Linear(hidden_size, out_size),
        )
    else:
        module = nn.Sequential(
            nn.Linear(in_size, hidden_size),
            nn.GELU(),
            nn.Linear(hidden_size, hidden_size),
            nn.GELU(),
            nn.Linear(hidden_size, out_size),
        )
    if lay_norm:
        return nn.Sequential(module, nn.LayerNorm(normalized_shape=out_size))
    return module


def build_mlp_test(
    in_size,
    hidden_size,
    out_size,
    drop_out=False,
    lay_norm=True,
    dropout_prob=0.2,
    specify_hidden_layer_num=2,
):
    layers = []
    layers.append(nn.Linear(in_size, hidden_size))
    if drop_out:
        layers.append(nn.Dropout(p=dropout_prob))
    layers.append(nn.GELU())

    # Add specified number of hidden layers
    for i in range(specify_hidden_layer_num - 1):
        layers.append(nn.Linear(hidden_size, hidden_size))
        if drop_out:
            layers.append(nn.Dropout(p=dropout_prob))
        layers.append(nn.GELU())

    layers.append(nn.Linear(hidden_size, out_size))

    if lay_norm:
        layers.append(nn.LayerNorm(normalized_shape=out_size))

    return nn.Sequential(*layers)


class Encoder(nn.Module):
    def __init__(
        self,
        node_input_size=128,
        edge_input_size=128,
  
        hidden_size=128,
 
    ):
        super(Encoder, self).__init__()

        self.eb_encoder = build_mlp(
            edge_input_size, hidden_size, int(hidden_size), drop_out=False
        )
        self.nb_encoder = build_mlp(
            node_input_size, hidden_size, int(hidden_size), drop_out=False
        )



    def forward(self, graph_node):
        (
            node_attr,
            edge_index,
            edge_attr,
            face,
            _,
            _,
        ) = decompose_and_trans_node_attr_to_cell_attr_graph(
            graph_node, has_changed_node_attr_to_cell_attr=False
        )

        # cell_ = self.cb_encoder(cell_attr)*mask_cell_interior.view(-1,1).long()
        node_ = self.nb_encoder(node_attr)
        edge_ = self.eb_encoder(edge_attr)

        return (
            Data(x=node_, edge_attr=edge_, edge_index=edge_index, face=face),
            edge_,
            node_,
        )


class GnBlock(nn.Module):
    def __init__(self, hidden_size=128):
        super(GnBlock, self).__init__()

        eb_input_dim = int(3 * (hidden_size))
        nb_input_dim = int(hidden_size + (hidden_size / 2.0))
        # cb_input_dim = 2 * hidden_size
        # cb_custom_func = build_mlp(cb_input_dim, hidden_size, hidden_size,drop_out=False)
        # self.cb_module = CellBlock(hidden_size,hidden_size,attention=attention,MultiHead=MultiHead,custom_func=cb_custom_func)
        nb_custom_func = build_mlp(
            nb_input_dim, hidden_size, int(hidden_size), drop_out=False
        )
        self.nb_module = NodeBlock(
   
            custom_func=nb_custom_func,
        )
        eb_custom_func = build_mlp(
            eb_input_dim, hidden_size, int(hidden_size), drop_out=False
        )
        self.eb_module = EdgeBlock(custom_func=eb_custom_func)

    def forward(self, graph_node):
        graph_node_last = copy_geometric_data(
            graph_node, has_changed_node_attr_to_cell_attr=True
        )
        # last_cell_attr = graph_last.x

        graph_node = self.eb_module(graph_node)

        graph_node = self.nb_module(graph_node)

        # resdiual connection
        x = graph_node.x + graph_node_last.x
        edge_attr = graph_node.edge_attr + graph_node_last.edge_attr

        return Data(
            x=x,
            edge_attr=edge_attr,
            edge_index=graph_node.edge_index,
            face=graph_node.face,
        )


class Decoder(nn.Module):
    def __init__(
        self,
        hidden_size=128,
 

        node_output_size=2,

    ):
        super(Decoder, self).__init__()


        self.node_decode_module = build_mlp_test(
            1 * int((hidden_size)),
            hidden_size,
            node_output_size,
            drop_out=False,
            lay_norm=False,
            specify_hidden_layer_num=2,
        )

    def forward(self, node_embedding=None, latent_graph_node=None):
        node_attr, _, _, _, _, _ = decompose_and_trans_node_attr_to_cell_attr_graph(
            latent_graph_node, has_changed_node_attr_to_cell_attr=True
        )

        # node_decode_attr = self.node_decode_module(
        #     torch.cat((node_attr, node_embedding), dim=1)
        # )
        
        node_decode_attr = self.node_decode_module(node_attr)
        
        return node_decode_attr


    
class EncoderProcesser(nn.Module):

    def __init__(self, message_passing_num, edge_input_size,node_input_size,hidden_size=128):

        super(EncoderProcesser, self).__init__()

        self.encoder = Encoder(node_input_size=node_input_size,edge_input_size=edge_input_size, hidden_size=hidden_size)
        self.message_passing_num = message_passing_num
        processer_list = []
        for _ in range(message_passing_num):
            processer_list.append(GnBlock(hidden_size=hidden_size))
        self.processer_list = nn.ModuleList(processer_list)
        
       
        

        
    def forward(self, graph_node):

        latent_graph_node,_,node_embedding= self.encoder(graph_node)
        # graph_embeded = copy_geometric_data(latent_graph_cell,has_changed_node_attr_to_cell_attr=True)
        # count = self.message_passing_num
        for model in self.processer_list:
            latent_graph_node = model(latent_graph_node)

        # decode latent graph to node attr a and p
        
      
        return latent_graph_node.x

class EulerSolver(nn.Module):
    def __init__(self,graph_edge,graph_cell,graph_map,graph_Index) :
        super(EulerSolver,self).__init__()

        self.d_face,self.u_face = graph_edge.du_face
        self.r_face,self.l_face = graph_edge.lr_face
        self.to_face_ll_cell,self.to_face_l_cell,self.to_face_r_cell,self.to_face_rr_cell = graph_map.to_face_lr_cell_index
        self.to_face_uu_cell,self.to_face_u_cell,self.to_face_d_cell,self.to_face_dd_cell = graph_map.to_face_du_cell_index
        
        self.to_cell_l_cell,self.to_cell_r_cell = graph_map.to_cell_lr_cell_index
        self.to_cell_u_cell,self.to_cell_d_cell = graph_map.to_cell_du_cell_index



        self.map_index = graph_cell.map_index
        self.target_q = graph_edge.y
        dtau = graph_Index.dtau
        self.area = graph_cell.area  
        self.interpolate_stencil = graph_cell.face
        self.dtau = dtau[graph_cell.batch]
        self.face_metrics = graph_edge.x
        self.face_type = graph_edge.face_type
        self.face_interpolate_flag = graph_edge.face_interpolate_flag
        self.zero_mask = graph_map.x



    def spatial_discretization_2nd_order(self,q):

        # 修复：创建副本避免修改原始输入
        mapped_flow_fields = q[self.map_index].clone()

        interpolated_face_attr = self.nonlinear_interpolation(mapped_flow_fields,q)
        
        face_attr_with_bc = self.enforce_bc_weak(interpolated_face_attr)

        norm_x,norm_y,length = self.face_metrics[:,0:1],self.face_metrics[:,1:2],self.face_metrics[:,2:3]
        # 黎曼求解器
        face_flux = self.riemann_solver(face_attr_with_bc[:,0,:], face_attr_with_bc[:,1,:], norm_x, norm_y,length)

        ##########i增加方向为xi,j增加方向为eta#######################
        dxi_face_flux = face_flux[self.d_face]-face_flux[self.u_face]
        deta_face_flux = face_flux[self.r_face]-face_flux[self.l_face]

        # 修正：除以单元面积进行归一化
        rhs = -(dxi_face_flux+deta_face_flux) / self.area
        
        return rhs
    
    def nonlinear_interpolation(self,mapped_flow_fields,original_flow_fields):
        ###############内部单元面左右值########################
        xi_flow_l = 1.5*mapped_flow_fields[self.to_face_u_cell]-0.5*mapped_flow_fields[self.to_face_uu_cell]
        xi_flow_r = 1.5*mapped_flow_fields[self.to_face_d_cell]-0.5*mapped_flow_fields[self.to_face_dd_cell]

        eta_flow_l = 1.5*mapped_flow_fields[self.to_face_l_cell]-0.5*mapped_flow_fields[self.to_face_ll_cell]
        eta_flow_r = 1.5*mapped_flow_fields[self.to_face_r_cell]-0.5*mapped_flow_fields[self.to_face_rr_cell]

        face_attr = torch.cat((torch.stack((xi_flow_l,xi_flow_r),dim=1),torch.stack((eta_flow_l,eta_flow_r),dim=1)),dim=0)

        ###############边界插值########################
        wall_mask = self.face_type==NodeType.WALL_BOUNDARY

        far_field_mask = self.face_type==NodeType.FARFIELD

        boundary_mask = torch.logical_or(wall_mask,far_field_mask)

        boundary_face_flag = self.face_interpolate_flag[boundary_mask]

        boundary_indices = torch.where(boundary_mask)[0] 

        channel_indices = (~boundary_face_flag).long() 

        source,target = (self.interpolate_stencil.T)[boundary_mask].T

        face_attr[boundary_indices, channel_indices] = 1.5*original_flow_fields[source]-0.5*original_flow_fields[target]

        near_boundary_mask = self.face_type==NodeType.NEAR_BOUNDARY

        near_boundary_face_flag = self.face_interpolate_flag[near_boundary_mask]

        near_boundary_indices = torch.where(near_boundary_mask)[0] 

        channel_indices_nearl_wall = (~near_boundary_face_flag).long() 

        face_attr[near_boundary_indices, channel_indices_nearl_wall] = 0.5*original_flow_fields[source]+0.5*original_flow_fields[target]

        return face_attr
    
    
    def enforce_bc_weak(self,flow_fields):

        wall_mask = self.face_type==NodeType.WALL_BOUNDARY

        wall_face_flag = self.face_interpolate_flag[wall_mask]

        wall_indices = torch.where(wall_mask)[0] 

        wall_metrics = self.face_metrics[wall_mask]
        
        wall_face_velocity = flow_fields[wall_indices,:,0:2]  # [N_wall, 2, 2]
        
        # 提取法向量和长度
        normal_x = wall_metrics[:, 0:1]  # [N_wall, 1]
        normal_y = wall_metrics[:, 1:2]  # [N_wall, 1] 
        normal_length = wall_metrics[:, 2:3]  # [N_wall, 1]
        
        # 单位法向量
        unit_normal = torch.cat([normal_x, normal_y], dim=-1) / normal_length  # [N_wall, 2]
        
        # 根据flag确定ghost和内部值的位置
        # False: 左值=ghost, 右值=内部
        # True:  左值=内部, 右值=ghost
        
        # 提取内部速度值
        internal_velocity = torch.where(
            wall_face_flag.unsqueeze(-1).unsqueeze(-1),  # [N_wall, 1, 1]
            wall_face_velocity[:, 0:1, :],  # flag=True时，左值是内部值
            wall_face_velocity[:, 1:2, :]   # flag=False时，右值是内部值
        )  # [N_wall, 1, 2]
        
        internal_velocity = internal_velocity.squeeze(1)  # [N_wall, 2]
        
        # 计算法向速度分量
        normal_velocity = torch.sum(internal_velocity * unit_normal, dim=-1, keepdim=True)  # [N_wall, 1]
        
        # 计算ghost速度 (镜像反射：去除法向分量的两倍)
        ghost_velocity = internal_velocity - 2 * normal_velocity * unit_normal  # [N_wall, 2]
        
        # 将ghost速度赋值回对应位置
        ghost_channel = wall_face_flag.long()  # False->0(左值), True->1(右值)
        
        # 赋值ghost速度
        wall_face_velocity[torch.arange(wall_face_velocity.size(0)), ghost_channel] = ghost_velocity
        
        # 将修改后的速度写回flow_fields
        flow_fields[wall_indices, :, 0:2] = wall_face_velocity
        
        # 处理压力和密度 (通常在壁面边界条件下保持相等)
        wall_face_pressure_density = flow_fields[wall_indices, :, 2:4]  # [N_wall, 2, 2] (压力+密度)
        
        # 提取内部压力和密度值
        internal_pd = torch.where(
            wall_face_flag.unsqueeze(-1).unsqueeze(-1),  # [N_wall, 1, 1]
            wall_face_pressure_density[:, 0:1, :],  # flag=True时，左值是内部值
            wall_face_pressure_density[:, 1:2, :]   # flag=False时，右值是内部值
        ).squeeze(1)  # [N_wall, 2]
        
        # Ghost单元的压力和密度等于内部值 (Neumann边界条件)
        wall_face_pressure_density[torch.arange(wall_face_pressure_density.size(0)), ghost_channel] = internal_pd
        
        # 写回压力和密度
        flow_fields[wall_indices, :, 2:4] = wall_face_pressure_density


        # 处理远场边界条件
        far_field_mask = self.face_type==NodeType.FARFIELD

        far_field_face_flag = self.face_interpolate_flag[far_field_mask]

        far_field_indices = torch.where(far_field_mask)[0] 

        far_field_face_attr = flow_fields[far_field_indices,:,:]  # [N_farfield, 2, 4]

        target_q = self.target_q[far_field_mask]  # [N_farfield, 4]

        # 根据flag确定ghost位置
        # True:  左值=内部, 右值=ghost
        # False: 左值=ghost, 右值=内部
        ghost_channel = far_field_face_flag.long()  # True->1(右值), False->0(左值)
        
        # 将target_q赋值给ghost位置
        far_field_face_attr[torch.arange(far_field_face_attr.size(0)), ghost_channel] = target_q
        
        # 将修改后的远场face数据写回flow_fields
        flow_fields[far_field_indices, :, :] = far_field_face_attr

        return flow_fields

    
    def primes2cons(self,primes):

  

        u = primes[:,0:1]
        v = primes[:,1:2]
        p = primes[:,2:3]
        rho = primes[:,3:4]

    

        return torch.cat((rho*u,rho*v,p/(1.4-1)+0.5*rho*(u**2+v**2),rho),dim=-1)




    def cons2primes(self, cons, gamma=1.4):
        """
        cons: [N, 4]  (rho*u, rho*v, e, rho)
        return: [N, 4] (u, v, p, rho)
        """
        rho = cons[:, 3:4]
        u = cons[:, 0:1] / rho
        v = cons[:, 1:2] / rho
        E = cons[:, 2:3]
        p = (E - 0.5 * rho * (u ** 2 + v ** 2)) * (gamma - 1)

        return torch.cat((u,v,p,rho),dim=-1)

    def riemann_solver(self,q_l,q_r,metrics_x,metrics_y,area):

        flux_l = self.flux_function(q_l,metrics_x,metrics_y,area,1.0)

        flux_r = self.flux_function(q_r,metrics_x,metrics_y,area,-1.0)

        face_flux = flux_l+flux_r

        return face_flux

    
    def flux_function(self,prime,metrics_x, metrics_y,area,fsw):
        """
        保留原来的Steger-Warming通量分裂方法（兼容性）
        """

        u = prime[:,0:1]
        v = prime[:,1:2]
        p = prime[:,2:3]
        rho = prime[:,3:4]

        gamma = 1.4
        c = torch.sqrt(gamma*p/rho)

        velocity_square = u**2+v**2

        H = c**2/(1.4-1)+0.5*velocity_square

        E = c**2/(1.4-1)+0.5*velocity_square-p/rho

        projected_velocity = metrics_x * u + metrics_y * v

        face_length = torch.maximum(area,torch.tensor(1e-30))

        metrics_x_norm = metrics_x/face_length

        metrics_y_norm = metrics_y/face_length

        projected_velocity_norm = projected_velocity/face_length

        l1 = projected_velocity
        l4 = projected_velocity+face_length*c
        l5 = projected_velocity-face_length*c

        al1 = self.enfix_steger(l1,0.0)
        al4 = self.enfix_steger(l4,0.0)
        al5 = self.enfix_steger(l5,0.0)

        l1 = 0.5 * (l1 + fsw * al1)
        l4 = 0.5 * (l4 + fsw * al4)
        l5 = 0.5 * (l5 + fsw * al5)

        c2r = c**2 /1.4

        x1 = c2r * (2 * l1 - l4 - l5) / (2 * c**2)

        x2 = c2r * (l4 - l5) / (2 * c)

        flux_u= (l1 * u - x1 * u + x2 * metrics_x_norm) * rho

        flux_v= (l1 * v - x1 * v + x2 * metrics_y_norm) * rho

        flux_e= (l1 * E - x1 * H + x2 * projected_velocity_norm) * rho

        flux_rho= (l1 - x1) * rho

        return torch.cat([flux_u,flux_v,flux_e,flux_rho],dim=-1)

    def enfix_steger(self, l0, eps):
        l1 = torch.sqrt(l0 ** 2 + eps ** 2)
        return l1
    

    def ausm_flux(self, prime_l, prime_r, metrics_x, metrics_y, area):    
        gamma = 1.4
        u_l, v_l, p_l, rho_l = prime_l[:, 0], prime_l[:, 1], prime_l[:, 2], prime_l[:, 3]
        u_r, v_r, p_r, rho_r = prime_r[:, 0], prime_r[:, 1], prime_r[:, 2], prime_r[:, 3]

        a_l = torch.sqrt(gamma * p_l / (rho_l + 1e-12))
        a_r = torch.sqrt(gamma * p_r / (rho_r + 1e-12))

 
        h_l = gamma / (gamma - 1) * p_l / rho_l + 0.5 * (u_l ** 2 + v_l ** 2)
        h_r = gamma / (gamma - 1) * p_r / rho_r + 0.5 * (u_r ** 2 + v_r ** 2)

        nx = metrics_x.squeeze(-1)
        ny = metrics_y.squeeze(-1)
        area = area.squeeze(-1)

        # Normal velocity
        un_l = u_l * nx + v_l * ny
        un_r = u_r * nx + v_r * ny

        # Mach numbers
        M_l = un_l / (a_l + 1e-12)
        M_r = un_r / (a_r + 1e-12)

        # Left state AUSM split
        M_plus = torch.where(M_l <= -1.0, torch.zeros_like(M_l),
                            torch.where(M_l < 1.0, 0.25 * (M_l + 1.0)**2, M_l))
        P_plus = torch.where(M_l <= -1.0, torch.zeros_like(p_l),
                            torch.where(M_l < 1.0, 0.25 * p_l * (M_l + 1)**2 * (2 - M_l), p_l))

        # Right state AUSM split
        M_minus = torch.where(M_r <= -1.0, M_r,
                            torch.where(M_r < 1.0, -0.25 * (M_r - 1.0)**2, torch.zeros_like(M_r)))
        P_minus = torch.where(M_r <= -1.0, p_r,
                            torch.where(M_r < 1.0, 0.25 * p_r * (M_r - 1)**2 * (2 + M_r), torch.zeros_like(p_r)))

        # Mass flux
        M_pm = M_plus + M_minus
        mass_flux = 0.5 * M_pm * (rho_l * a_l + rho_r * a_r) * area
        mass_flux += 0.5 * torch.abs(M_pm) * (rho_l * a_l - rho_r * a_r) * area

        # Momentum flux
        mom_flux_x = 0.5 * M_pm * (rho_l * a_l * u_l + rho_r * a_r * u_r) * area
        mom_flux_x += 0.5 * torch.abs(M_pm) * (rho_l * a_l * u_l - rho_r * a_r * u_r) * area
        mom_flux_x += (P_plus + P_minus) * nx

        mom_flux_y = 0.5 * M_pm * (rho_l * a_l * v_l + rho_r * a_r * v_r) * area
        mom_flux_y += 0.5 * torch.abs(M_pm) * (rho_l * a_l * v_l - rho_r * a_r * v_r) * area
        mom_flux_y += (P_plus + P_minus) * ny

        # Energy flux
        energy_flux = 0.5 * M_pm * (rho_l * a_l * h_l + rho_r * a_r * h_r) * area
        energy_flux += 0.5 * torch.abs(M_pm) * (rho_l * a_l * h_l - rho_r * a_r * h_r) * area

        face_flux = torch.stack([
            mom_flux_x,
            mom_flux_y,
            energy_flux,
            mass_flux
        ], dim=-1)

        return face_flux



    def roe_flux(self,
                prime_l, prime_r,
                metrics_x, metrics_y, area,
                gamma: float = 1.4,
                entropy_fix_coef: float = 0.1):
        """
        prime_*: [N, 4] -> (u, v, p, rho)
        metrics_x, metrics_y, area: [N] or [N,1]
        返回: [N, 4] -> (mom_x, mom_y, energy, mass) 通量
        """

        # ---------- 解包 ----------
        u_l, v_l, p_l, rho_l = prime_l[:, 0], prime_l[:, 1], prime_l[:, 2], prime_l[:, 3]
        u_r, v_r, p_r, rho_r = prime_r[:, 0], prime_r[:, 1], prime_r[:, 2], prime_r[:, 3]

        nx = metrics_x.squeeze(-1) if metrics_x.dim() > 1 else metrics_x
        ny = metrics_y.squeeze(-1) if metrics_y.dim() > 1 else metrics_y
        A  = area.squeeze(-1)      if area.dim()      > 1 else area

        # ---------- 保守量 ----------
        # 总能量 E = p/(gamma-1)/rho + 0.5*(u^2+v^2)
        E_l = p_l / ((gamma - 1.0) * rho_l + 1e-12) + 0.5 * (u_l**2 + v_l**2)
        E_r = p_r / ((gamma - 1.0) * rho_r + 1e-12) + 0.5 * (u_r**2 + v_r**2)

        # phi = [rho, rho*u, rho*v, rho*E]
        phi_l0 = rho_l
        phi_l1 = rho_l * u_l
        phi_l2 = rho_l * v_l
        phi_l3 = rho_l * E_l

        phi_r0 = rho_r
        phi_r1 = rho_r * u_r
        phi_r2 = rho_r * v_r
        phi_r3 = rho_r * E_r

        # ---------- 法向速度 ----------
        un_l = u_l * nx + v_l * ny
        un_r = u_r * nx + v_r * ny

        # ---------- 物理通量 F_L, F_R ----------
        HL = (phi_l3 + p_l) / (rho_l + 1e-12)  # 总焓
        HR = (phi_r3 + p_r) / (rho_r + 1e-12)

        FL0 = rho_l * un_l
        FL1 = phi_l1 * un_l + p_l * nx
        FL2 = phi_l2 * un_l + p_l * ny
        FL3 = (phi_l3 + p_l) * un_l

        FR0 = rho_r * un_r
        FR1 = phi_r1 * un_r + p_r * nx
        FR2 = phi_r2 * un_r + p_r * ny
        FR3 = (phi_r3 + p_r) * un_r

        # ---------- Roe 平均 ----------
        sqrt_r_l = torch.sqrt(rho_l + 1e-12)
        sqrt_r_r = torch.sqrt(rho_r + 1e-12)
        denom     = sqrt_r_l + sqrt_r_r + 1e-12

        ui = (sqrt_r_l * u_l + sqrt_r_r * u_r) / denom
        vi = (sqrt_r_l * v_l + sqrt_r_r * v_r) / denom
        Hi = (sqrt_r_l * HL  + sqrt_r_r * HR ) / denom

        af  = 0.5 * (ui**2 + vi**2)
        ucp = ui * nx + vi * ny

        c2 = (gamma - 1.0) * (Hi - af)
        c  = torch.sqrt(torch.clamp(c2, min=1e-12))
        inv_c  = 1.0 / (c + 1e-12)
        inv_c2 = 1.0 / (c2 + 1e-12)

        # ---------- 特征值 ----------
        lam1 = ucp + c
        lam2 = ucp - c
        lam3 = ucp  # 重根（两重），这里只用一个即可，公式里用到的是 l3

        # ---------- 熵修正 ----------
        eps = entropy_fix_coef * c
        def entropy_fix(lam, eps_):
            return torch.where(torch.abs(lam) < eps_,
                            0.5 * (eps_ + lam * lam / (eps_ + 1e-12)),
                            torch.abs(lam))

        lam1 = entropy_fix(lam1, eps)
        lam2 = entropy_fix(lam2, eps)
        lam3 = entropy_fix(lam3, eps)

        s1 = 0.5 * (lam1 + lam2)
        s2 = 0.5 * (lam1 - lam2)

        # ---------- 状态差分 ----------
        d0 = phi_r0 - phi_l0
        d1 = phi_r1 - phi_l1
        d2 = phi_r2 - phi_l2
        d3 = phi_r3 - phi_l3

        # ---------- G1 / G2 ----------
        G1 = (gamma - 1.0) * (af * d0 - ui * d1 - vi * d2 + d3)
        G2 = -ucp * d0 + d1 * nx + d2 * ny

        # ---------- C1 / C2 ----------
        C1 = G1 * (s1 - lam3) * inv_c2 + G2 * s2 * inv_c
        C2 = G1 * s2 * inv_c    + G2 * (s1 - lam3)

        # ---------- Roe 通量 ----------
        F0 = 0.5 * (FL0 + FR0) - 0.5 * (lam3 * d0 + C1)
        F1 = 0.5 * (FL1 + FR1) - 0.5 * (lam3 * d1 + C1 * ui + C2 * nx)
        F2 = 0.5 * (FL2 + FR2) - 0.5 * (lam3 * d2 + C1 * vi + C2 * ny)
        F3 = 0.5 * (FL3 + FR3) - 0.5 * (lam3 * d3 + C1 * Hi + C2 * ucp)

        # 乘面元
        F0 *= A
        F1 *= A
        F2 *= A
        F3 *= A

        face_flux = torch.stack([F1, F2, F3, F0], dim=-1)  # 顺序与 ausm_flux 一致: (mom_x, mom_y, energy, mass)
        return face_flux        

   

    def solve_RK3(self, primes):
        """
        修正后的三阶TVD Runge-Kutta求解方法
        
        Args:
            primes: 初始原始变量 [N, 4]
            num_iterations: 固定为3（三阶RK）
            
        Returns:
            primes: 更新后的原始变量 [N, 4]
        """
        # 转换为守恒变量
        Q0 = self.primes2cons(primes)  # 初始状态
        
        # 三阶TVD Runge-Kutta (Shu-Osher格式)
        
        # 第1步：Q1 = Q0 + dt*L(Q0)
        rhs0 = self.spatial_discretization_2nd_order(primes)
        Q1 = Q0 + self.dtau.unsqueeze(-1) * rhs0
        primes1 = self.cons2primes(Q1)
        
        # 第2步：Q2 = 3/4*Q0 + 1/4*Q1 + 1/4*dt*L(Q1)
        rhs1 = self.spatial_discretization_2nd_order(primes1)
        Q2 = 0.75 * Q0 + 0.25 * Q1 + 0.25 * self.dtau.unsqueeze(-1) * rhs1
        primes2 = self.cons2primes(Q2)
        
        # 第3步：Q_new = 1/3*Q0 + 2/3*Q2 + 2/3*dt*L(Q2)
        rhs2 = self.spatial_discretization_2nd_order(primes2)
        Q_new = (1.0/3.0) * Q0 + (2.0/3.0) * Q2 + (2.0/3.0) * self.dtau.unsqueeze(-1) * rhs2
        
        # 保存最后一次的rhs用于监控
        self.last_rhs = rhs2.detach()
        
        # 转换回原始变量
        primes_new = self.cons2primes(Q_new)
        
        return primes_new
    

    def calc_spectral_radius(self,primes):

        a = torch.sqrt(1.4*primes[:,2:3]/(primes[:,3:4]))

        cell_xi_metrics = (self.face_metrics[self.d_face]+self.face_metrics[self.u_face])/2

        cell_eta_metrics = (self.face_metrics[self.l_face]+self.face_metrics[self.r_face])/2

        vn_xi = torch.sum(primes[:,0:2]*cell_xi_metrics[:,0:2],dim=-1,keepdim=True)

        vn_eta = torch.sum(primes[:,0:2]*cell_eta_metrics[:,0:2],dim=-1,keepdim=True)

        length_xi = torch.sqrt(torch.sum(cell_xi_metrics[:,0:2]**2,dim=-1,keepdim=True))

        length_eta = torch.sqrt(torch.sum(cell_eta_metrics[:,0:2]**2,dim=-1,keepdim=True))

        rca = torch.abs(vn_xi)+a*length_xi

        rcb = torch.abs(vn_eta)+a*length_eta

        src = torch.cat([rca,rcb],dim=-1)

        rdt = rca + rcb

        return src, rdt,torch.cat((cell_xi_metrics[:,0:2],length_xi),dim=-1),torch.cat((cell_xi_metrics[:,0:2],length_eta),dim=-1)
    

    def calc_time_step(self,):

        pass

    def solv_lusgs(self,primes):

        dq = torch.zeros_like(primes)

        rhs = self.spatial_discretization_2nd_order(primes)

        src,rdt,cell_xi_metrics,cell_eta_metrics = self.calc_spectral_radius(primes)

        values_to_be_mapped = torch.cat([primes,src,rdt,dq,cell_xi_metrics,cell_eta_metrics],dim=-1)

        mapped_values = values_to_be_mapped[self.map_index]



        #################forward#####################

        u_cell_value = mapped_values[self.to_cell_u_cell]

        l_cell_value = mapped_values[self.to_cell_l_cell]

        u_primes = u_cell_value[:,0:4]

        u_metrics_xi = u_cell_value[:,11:14]

        u_dq = u_cell_value[:,7:11]
        
        u_src = u_cell_value[:,4:6]


        l_primes = l_cell_value[:,0:4]

        l_metrics_eta = l_cell_value[:,14:17]

        l_dq = l_cell_value[:,7:11]
        
        l_src = l_cell_value[:,4:6]

        ul_primes = torch.cat((u_primes,l_primes),dim=0)

        ul_metrics = torch.cat((u_metrics_xi,l_metrics_eta),dim=0)

        ul_dq = torch.cat((u_dq,l_dq),dim=0)

        ul_src = torch.cat((u_src,l_src),dim=0)

        ul_dflux = self.mxdq_std(ul_primes,ul_metrics,ul_dq,ul_src,1.0)











    
        a=1















    def mxdq_std(self,primes,metrics,dq,srad,fsw):

        u,v,p,rho = primes[:,0:1],primes[:,1:2],primes[:,2:3],primes[:,3:4]

        v_square =u**2+v**2

        c = torch.sqrt(1.4*p/rho)

        h = c**2/(1.4-1.0)

        h0 = h+0.5*v_square

        projected_velocity = u*metrics[:,0:1]+v*metrics[:,1:2]

        length = metrics[:,2:3]

        projected_velocity_norm = projected_velocity/length

        metrics_norm = metrics[:,0:2]/length

        l1 = 0.5*(projected_velocity + fsw*srad)

        l4 = 0.5*((projected_velocity+c*length) + fsw*srad)

        l5 = 0.5*((projected_velocity-c*length) + fsw*srad)

        x1 = ( 2*l1 - l4 - l5 )/( 2 * c**2 )

        x2 = ( l4 - l5 )/( 2 * c )
  
        af = 0.5*(1.4-1.0)*v_square

        y1 = projected_velocity_norm * dq[:,3:4] -  ( metrics_norm[:,0:1] * dq[:,0:1]  + metrics_norm[:,1:2] * dq[:,1:2] )
        y2 = af  * dq[:,3:4] - (1.4-1.0) * ( u  * dq[:,0:1] +  v * dq[:,1:2]  - dq[:,2:3] )  

        rcl = c**2*x1*y1 + x2*y2
        sl  = x1*y2 + x2*y1

        dflux_u =  l1 * dq[:,0:1] - u*sl + metrics_norm[:,0:1]*rcl 

        dflux_v =  l1 * dq[:,1:2] - v*sl + metrics_norm[:,1:2]*rcl 

        dflux_e =  l1 * dq[:,2:3] - h0*sl + projected_velocity_norm*rcl 

        dflux_rho =  l1 * dq[:,3:4] - sl 

        return torch.cat([dflux_u,dflux_v,dflux_e,dflux_rho],dim=-1)







       







