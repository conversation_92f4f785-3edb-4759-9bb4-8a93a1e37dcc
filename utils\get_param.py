import argparse
import json
import os
import random
import itertools
import numpy as np

def str2bool(v):
    """
    'boolean type variable' for add_argument
    """
    if v.lower() in ('yes','true','t','y','1'):
        return True
    elif v.lower() in ('no','false','f','n','0'):
        return False
    else:
        raise argparse.ArgumentTypeError('boolean value expected.')

def params(load=None):
    if load is not None:
        parser = argparse.ArgumentParser(description='train / test a pytorch model to predict frames')
        params = vars(parser.parse_args())
        with open(load+'/commandline_args.json', 'rt') as f:
            params.update(json.load(f))
        for k, v in params.items():
            parser.add_argument('--' + k, default=v)
        args = parser.parse_args()
        return  args
    else:
        """
        return parameters for training / testing / plotting of models
        :return: parameter-Namespace
        """
        parser = argparse.ArgumentParser(description='train / test a pytorch model to predict frames')
        # Training parameters
        parser.add_argument('--net', default="GFD-Euler", type=str, help='network to train (default: GN-Cell)', choices=["GFD-Euler"])
        parser.add_argument('--n_epochs', default=100000, type=int, help='number of epochs (after each epoch, the model gets saved)')
        parser.add_argument('--batch_size', default=1, type=int, help='batch size (default: 100)')
   

        parser.add_argument('--average_sequence_length', default=300, type=int, help='average sequence length in dataset=dataset_size/batch_size(default: 5000)')
        

        parser.add_argument('--dataset_size', default=1, type=int, help='size of dataset (default: 1000)')
  
        parser.add_argument('--all_on_gpu', default=False, type=str2bool, help='whether put all dataset on GPU')
        parser.add_argument('--lr', default=1e-4, type=float, help='learning rate of optimizer (default: 0.0001)')
        parser.add_argument('--milestones', default=[1000,2000,20000], type=list)
        parser.add_argument('--gamma', default=0.1, type=float)
        parser.add_argument('--log', default=True, type=str2bool, help='log models / metrics during training (turn off for debugging)')
        parser.add_argument('--rollout', default=False, type=str2bool, help='rolling out or not (turn off for debugging)')
        parser.add_argument('--on_gpu', default=0, type=int, help='set training on which gpu')
        # Solver parameters

        # train strategy parameters


        parser.add_argument('--dimless', default=True, type=str2bool, help='dimless')

       
    
        parser.add_argument('--max_inner_steps', default=1, type=int, help='unsteady time stepping convergence max iteration steps')

    

  
        # Load parameters
        parser.add_argument('--load_date_time', default=None, type=str, help='date_time of run to load (default: None)')
        parser.add_argument('--load_index', default=None , type=int, help='index of run to load (default: None)')
        parser.add_argument('--load_optimizer', default=False, type=str2bool, help='load state of optimizer (default: True)')
        parser.add_argument('--load_latest', default=False, type=str2bool, help='load latest version for training (if True: leave load_date_time and load_index None. default: False)')
        
        #model parameters
        parser.add_argument('--hidden_size', default=64, type=int, help='hidden size of network (default: 20)')
        parser.add_argument('--message_passing_num', default=12, type=int, help='message passing layer number (default:15)')
        parser.add_argument('--node_input_size', default=9+2, type=int, help='node encoder node_input_size (default: 2)')
        parser.add_argument('--edge_input_size', default=3, type=int, help='edge encoder edge_input_size, include edge center pos (x,y) (default: 3)')

      

        parser.add_argument('--edge_normlizer_input_size', default=3, type=int, help='edge normlizer edge_input_size (default: 2)')

        parser.add_argument('--node_output_size', default=3, type=int, help='edge decoder edge_output_size uvp on edge center(default: 8)')


        #dataset params
        
        parser.add_argument('--dataset_dir', default="./grid_example/NACA0012_from_NNfoil", type=str, help='load latest version for training (if True: leave load_date_time and load_index None. default: False)')

        #git information

        params = parser.parse_args()

        return params
            


def get_hyperparam(params):
    return f" {params.net}-hs {params.hidden_size};"

def generate_list(min_val, step, max_val):
    if min_val == step == max_val:
        return [max_val]
    else:
        # 使用linspace可以确保开始和结束的值都包括在内
        # 并根据步长计算必要的点数
        num_points = int((max_val - min_val) / step) + 1
        return list(np.linspace(min_val, max_val, num_points))
    
def generate_combinations(
    U_range=None, rho_range=None, mu_range=None, source_range=None, aoa_range=None, dtau=None, L=None,ma_range=None
):

    U_list = generate_list(*U_range)
    rho_list = generate_list(*rho_range)
    mu_list = generate_list(*mu_range)
    source_list = generate_list(*source_range)
    aoa_list = generate_list(*aoa_range)
    ma_list = generate_list(*ma_range)
    
    combinations = list(itertools.product(U_list, rho_list, mu_list, source_list, aoa_list,ma_list))

    valid_combinations = []
    valid_Re_values = []
    for U, rho, mu, source, aoa_list,ma_list in combinations:
        if rho==0.:
            rho=1.

    
        if mu==0.:
            Re = 12345
        else:
            Re = (U*rho*L) / mu
       
        valid_combinations.append([U, rho, mu, source, aoa_list, dtau, L,ma_list])
        valid_Re_values.append(Re)

    return valid_combinations
    
if __name__=='__main__':
    
    params_t,git_info = params()
    
    prefix="pf"
    
    if prefix=="cw":
        source_frequency_range = getattr(params_t, f"{prefix}_source_frequency")
        source_strength_range = getattr(params_t, f"{prefix}_source_strength")
        rho_range = getattr(params_t, f"{prefix}_rho")
        dt = getattr(params_t, f"{prefix}_dt")
        
        result = generate_combinations(source_frequency_range=source_frequency_range, source_strength_range=source_strength_range, rho_range=rho_range,dt=dt, eqtype="wave")
    else:
        U_range = getattr(params_t, f"{prefix}_inflow_range")
        rho_range = getattr(params_t, f"{prefix}_rho")
        mu_range = getattr(params_t, f"{prefix}_mu")
        source_range = getattr(params_t, f"{prefix}_source")
        aoa_range = getattr(params_t, f"{prefix}_aoa")
        Re_max = getattr(params_t, f"{prefix}_Re_max")
        Re_min = getattr(params_t, f"{prefix}_Re_min")
        dt = getattr(params_t, f"{prefix}_dt")
        L = getattr(params_t, f"{prefix}_L")

        result = generate_combinations(U_range, rho_range, mu_range, Re_max, Re_min, source_range, aoa_range, dt ,L=L, eqtype="fluid")
        
    # print(f"满足雷诺数 {Re_max} 限制的[U, rho, mu]组合是: {result}")